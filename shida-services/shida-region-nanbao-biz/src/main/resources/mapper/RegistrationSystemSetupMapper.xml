<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shida.region.biz.mapper.RegistrationSystemSetupMapper">


    <select id="selectEnrollStage" resultType="com.shida.region.biz.entity.RegistrationSystemSetup">
        SELECT t1.id,t1.name
        FROM t_registration_system_setup t1
        LEFT JOIN t_registration_system_setup t2
            ON t1.id = t2.parent_id
        WHERE t2.id = #{setUpSaveIds,jdbcType=BIGINT}

    </select>
</mapper>
