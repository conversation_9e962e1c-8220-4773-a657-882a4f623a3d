<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shida.region.biz.mapper.RegistrationTimeMapper">

    <select id="getPioneerBeginTime" resultType="java.time.LocalDateTime">
        SELECT begin_time
        FROM t_registration_time
        WHERE delete_flag = 0 AND begin_time IS NOT NULL
        ORDER BY begin_time LIMIT 1
    </select>

    <select id="getPioneerEndTime" resultType="java.time.LocalDateTime">
        SELECT end_time
        FROM t_registration_time
        WHERE delete_flag = 0
        AND begin_time IS NOT NULL
        AND `type` IN
        <foreach collection="typeList" index="index" item="item" open="(" separator="," close=")">
              #{item}
        </foreach>
        ORDER BY end_time DESC  LIMIT 1
    </select>


    <select id="getRegistrationTimeByTypeAndSetUpId" resultType="com.shida.region.biz.entity.RegistrationTime">
        SELECT `id`, `create_time`, `update_time`, `delete_flag`, `version`, `begin_time`, `end_time`, `status`, `type`,
        `setup_id`
        FROM t_registration_time
        WHERE delete_flag = 0
        AND begin_time &lt;= #{date} AND end_time &gt;= #{date}
        AND (type = #{type}
        <if test="setUpIdList != null">
            OR setup_id in
            <foreach collection="setUpIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        )
    </select>

    <select id="getRegistrationTimeVerify" resultType="com.shida.region.biz.entity.RegistrationTime">
        SELECT `id`, `create_time`, `update_time`, `delete_flag`, `version`, `begin_time`, `end_time`, `status`, `type`,
        `setup_id`
        FROM t_registration_time
        WHERE delete_flag = 0
        AND begin_time &lt;= #{date} AND end_time &gt;= #{date}
        AND (
        <if test="setUpIdList != null">
            type in
            <foreach collection="typeList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="setUpIdList != null">
            OR setup_id in
            <foreach collection="setUpIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        )
    </select>
    <select id="getRegistrationTimeBySetUpId" resultType="com.shida.region.biz.entity.RegistrationTime">
        SELECT `id`,
               `create_time`,
               `update_time`,
               `delete_flag`,
               `version`,
               `begin_time`,
               `end_time`,
               `status`,
               `type`,
               `setup_id`
        FROM t_registration_time
        WHERE delete_flag = 0
          AND begin_time &lt;= #{date}
          AND end_time &gt;= #{date}
          AND setup_id = #{setUpId}
    </select>
    <select id="getAllRegistrationTimeByDate" resultType="com.shida.region.biz.entity.RegistrationTime">
        SELECT `id`,
               `create_time`,
               `update_time`,
               `delete_flag`,
               `version`,
               `begin_time`,
               `end_time`,
               `status`,
               `type`,
               `setup_id`
        FROM t_registration_time
        WHERE delete_flag = 0
          AND begin_time &lt;= #{date}
          AND end_time &gt;= #{date}
    </select>
    <select id="getVerifyBySetUpId" resultType="com.shida.region.biz.entity.RegistrationTime">
        SELECT `id`,
               `create_time`,
               `update_time`,
               `delete_flag`,
               `version`,
               `begin_time`,
               `end_time`,
               `status`,
               `type`,
               `setup_id`
        FROM t_registration_time
        WHERE delete_flag = 0
          AND begin_time &lt;= #{date}
          AND end_time &gt;= #{date}
          AND setup_id = #{setUpId}
          LIMIT 1
    </select>
</mapper>
