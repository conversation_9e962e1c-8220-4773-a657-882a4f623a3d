<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shida.region.biz.mapper.RegistrationSystemSetUpSavaMapper">



    <!--CONCAT(t3.NAME,t2.NAME) 拼接 字符串 公办小学 -->
    <select id="getDetail" resultType="com.shida.region.biz.pojo.vo.signUpSortDetailSettingVo">
        SELECT t1.NAME AS signUpSort,t.detail ,CONCAT(t3.NAME,t2.NAME) AS field,t.setup_id
        FROM`t_registration_system_set_up_sava` t
        LEFT JOIN t_registration_system_setup t1 ON t1.id = t.setup_id
        LEFT JOIN t_registration_system_setup t2 ON t1.parent_id = t2.id
        LEFT JOIN t_registration_system_setup t3 ON t2.parent_id = t3.id
        WHERE t3.parent_id = #{id}
    </select>



    <!-- t.detail -->
    <select id="getSetupSaveIds" resultType="com.shida.region.biz.pojo.vo.registrationEntranceVo">
        SELECT distinct
        <if test="id == null or id == ''">t4.name , t4.id</if>
        <if test="id != null and id != ''">CONCAT(t4.NAME,t2.NAME)AS name,t2.id</if>
        FROM
            `t_registration_system_set_up_sava` t
                LEFT JOIN t_registration_system_setup t1 ON t1.id = t.setup_id
                LEFT JOIN t_registration_system_setup t2 ON t1.parent_id = t2.id
                LEFT JOIN t_registration_system_setup t3 ON t2.parent_id = t3.id
                LEFT JOIN t_registration_system_setup t4 ON t3.parent_id = t4.id
        <if test="id != null and id != ''">
            where t4.id = #{id}
        </if>
    </select>
</mapper>
