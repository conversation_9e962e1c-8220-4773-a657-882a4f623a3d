<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shida.region.biz.mapper.HomesteadLicenseInfoMapper">

    <update id="updateDeleteFlag">
        UPDATE r_homestead_license_info
        SET delete_flag = 0
        WHERE student_id = #{studentId}
    </update>
    <delete id="trueDelete">
        DELETE FROM r_homestead_license_info
        WHERE student_id = #{studentId}
    </delete>
</mapper>
