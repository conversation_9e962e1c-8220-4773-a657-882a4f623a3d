<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shida.region.biz.mapper.StudentBaseInfoMapper">
    <update id="updateDeleteFlag">
        UPDATE r_student_base_info
        SET delete_flag = 0
        WHERE student_id = #{studentId}
    </update>
    <delete id="trueDeleteById">
        DELETE
        FROM r_student_base_info
        WHERE student_id = #{studentId}
    </delete>

    <select id="selectSetUpIds" resultType="com.shida.region.biz.pojo.vo.EnrollStatusVO">
        SELECT
            COALESCE(MAX(CASE WHEN name = 'house' THEN 1 ELSE 0 END), 0) AS house,
            COALESCE(MAX(CASE WHEN name = 'hukou' THEN 1 ELSE 0 END), 0) AS hukou,
            COALESCE(MAX(CASE WHEN name = 'residence' THEN 1 ELSE 0 END), 0) AS residence
        FROM t_system_setup_data
        WHERE name IN ('house', 'hukou', 'residence')
          AND CONCAT(',', ids, ',') LIKE CONCAT('%', #{setupIds}, '%');
    </select>


    <select id="selectSetUpIdsName" resultType="java.lang.String">
        select ids FROM t_system_setup_data where
        <choose>
            <when test="publicSecurityReviewType == 1">
                name = 'hukou'
            </when>
            <when test="publicSecurityReviewType == 2">
                name = 'house'
            </when>
            <otherwise>
                name = 'residence'
            </otherwise>
        </choose>
    </select>
    <select id="getRecruitStudentPage" resultType="com.shida.region.biz.pojo.vo.RecruitStudentVo">
        SELECT
        sb.id AS student_base_id,
        sb.enroll_id,
        sb.set_up_save_ids,
        sb.is_related,

            sb.public_security_registration_review_status,
                sb.public_security_residence_review_status,
        sb.enroll_time,
        sb.enroll_school_id,
        sb.enroll_school_name,
        sb.school_review_status,
        sb.school_review_reason,
        sb.education_review_status,
        sb.education_review_reason,
        sb.estate_review_status,
        sb.estate_review_reason,
        sb.public_security_review_status,
        sb.public_security_review_reason,
        sb.check_in_status,
        sb.house_info_type,
        sb.adjust_school_id,
        sb.adjust_school_name,
        sb.adjustment,
        sb.is_zhi_sheng,
        sb.school_username,
        sb.school_operation_time,
        sb.education_username,
        sb.education_operation_time,
        sb.set_up_save_ids,
        sb.un_check_in_reason,
        sb.admission_letter_in_status,
        sb.publicity_status,
        sbi.reserve_field1 AS is_entitled_group,
        sbi.reserve_field2 AS entitled_group_type,
        sbi.enroll_number,
        sbi.student_name,
        sbi.student_id_card_number,
        sbi.student_brith_date,
        sbi.student_gender,
        sbi.student_nation,
        sbi.student_residence_address,
        sbi.student_current_address,
        sbi.child_vaccination_certificate,
        sbi.birth_certificate,
        save.ids_name AS type,
        td.nature
        FROM r_student_base AS sb
        LEFT JOIN r_student_base_info AS sbi ON sb.id = sbi.student_id
        LEFT JOIN t_registration_system_set_up_sava AS save ON sb.set_up_save_ids = save.setup_id
        LEFT JOIN `${dbName}`.t_dept AS td ON sb.enroll_school_id = td.id
        WHERE sb.delete_flag = 0
        <if test="search.spanStatus == 0 ">
            AND (sb.adjustment = 0 OR sb.adjustment IS NULL)
        </if>
        <if test="search.spanStatus == 1 ">
            AND sb.adjustment = 1
        </if>
        <!--报名阶段-->
        <if test="search.enrollStage != null and search.enrollStage != ''">
            AND sb.enroll_stage = #{search.enrollStage}
        </if>
        <if test="search.studentName != null and search.studentName != ''">
            AND (sbi.student_name like CONCAT('%',#{search.studentName},'%') or
            sbi.student_id_card_number like CONCAT('%',#{search.studentName},'%'))
        </if>
        <!--是否公示(0未公示 1 已公示)-->
        <if test="search.publicityStatus != null and search.publicityStatus != '' or search.publicityStatus == 0">
            AND sb.publicity_status = #{search.publicityStatus}
        </if>
        <!--是否多胞胎-->
        <if test="search.isRelated != null and search.isRelated != ''">
            AND sb.is_related = #{search.isRelated}
        </if>
        <if test="search.isZhiSheng != null and search.isZhiSheng != '' and search.isZhiSheng ==1 ">
            AND sb.is_zhi_sheng = #{search.isZhiSheng}
        </if>
        <if test="search.isZhiSheng == 0">
            AND sb.is_zhi_sheng != 1
        </if>
        <!--公安户口审核-->
        <if test="search.publicSecurityRegistrationReviewStatus != null and search.publicSecurityRegistrationReviewStatus != ''">
            AND sb.public_security_registration_review_status = #{search.publicSecurityRegistrationReviewStatus}
        </if>
        <!--公安居住证审核-->
        <if test="search.publicSecurityResidenceReviewStatus != null and search.publicSecurityResidenceReviewStatus != ''">
            AND sb.public_security_residence_review_status = #{search.publicSecurityResidenceReviewStatus}
        </if>
        <!--报名ID-->
        <if test="search.enrollId != null and search.enrollId != ''">
            AND sb.enroll_id like CONCAT('%',#{search.enrollId},'%')
        </if>
        <choose>
            <!--如果是学校账号
             OR (sb.adjust_school_id = #{search.enrollSchoolId})-->
            <when test="search.role != null and search.role == 'SCHOOL'">
                AND (
                (sb.enroll_school_id = #{search.enrollSchoolId} AND sb.adjust_school_id IS NULL )

                )
            </when>
            <otherwise>
                <!--学校ID-->
                <!--报名学校是A 调剂到B 那么 搜索条件是A 搜不到 搜索条件是B 搜不到，只有搜索条件是调剂学校才能搜到-->
                <if test="search.enrollSchoolId != null and search.enrollSchoolId != ''
                    and search.adjustSchoolId == null or search.adjustSchoolId == '' ">
                    AND sb.enroll_school_id = #{search.enrollSchoolId}
                    AND sb.adjust_school_id IS NULL
                </if>
                <!--调剂学校ID-->
                <if test="search.adjustSchoolId != null and search.adjustSchoolId != ''
                    and search.enrollSchoolId == null or search.enrollSchoolId == ''">
                    AND sb.adjust_school_id = #{search.adjustSchoolId}
                </if>
                <!--报名学校ID 调剂学校ID-->
                <if test="search.enrollSchoolId != null and search.enrollSchoolId != ''
                    and search.adjustSchoolId != null and search.adjustSchoolId != '' ">
                    AND sb.enroll_school_id = #{search.enrollSchoolId}
                    AND sb.adjust_school_id = #{search.adjustSchoolId}
                </if>
            </otherwise>
        </choose>

        <!--学校名称-->
        <if test="search.enrollSchoolName != null and search.enrollSchoolName != ''">
            AND sb.enroll_school_name like CONCAT('%',#{search.enrollSchoolName},'%')
        </if>
        <!--调剂学校名称-->
        <if test="search.adjustSchoolName != null and search.adjustSchoolName != ''">
            AND sb.adjust_school_name like CONCAT('%',#{search.adjustSchoolName},'%')
        </if>
        <!--学校性质-->
        <if test="search.nature != null and search.nature != ''">
            AND td.nature = #{search.nature}
        </if>
        <!--类别
        AND sb.set_up_save_ids = #{search.type}
         <if test="search.houseInfoType != null and search.houseInfoType != ''">
            AND sb.house_info_type = #{search.houseInfoType}
        </if>
        -->
        <if test="search.type != null and search.type != ''">
            AND save.ids_name like CONCAT('%',#{search.type},'%')
        </if>
        <!--报名开始时间,结束时间-->
        <if test="search.createdTime != null">
            AND sb.enroll_time &gt;= #{search.createdTime}
        </if>
        <if test="search.endTime != null">
            AND sb.enroll_time &lt;= #{search.endTime}
        </if>
        <!--学校审核状态-->
        <if test="search.schoolReviewStatus != null and search.schoolReviewStatus != ''">
            AND sb.school_review_status = #{search.schoolReviewStatus}
        </if>
        <!--教育局审核状态-->
        <if test="search.educationReviewStatus and search.educationReviewStatus != ''">
            AND sb.education_review_status = #{search.educationReviewStatus}
        </if>
        <!--公安审核状态-->
        <if test="search.publicSecurityReviewStatus != null and search.publicSecurityReviewStatus != ''">
            AND sb.public_security_review_status = #{search.publicSecurityReviewStatus}
        </if>
        <!--房产审核状态-->
        <if test="search.estateReviewStatus != null and search.estateReviewStatus != ''">
            AND sb.estate_review_status = #{search.estateReviewStatus}
        </if>
        <!--是否报道-->
        <if test="search.checkInStatus != null ">
            AND sb.check_in_status = #{search.checkInStatus}
        </if>
        <!--是否发送录取通知书-->
        <if test="search.admissionLetterInStatus != null ">
            AND sb.admission_letter_in_status = #{search.admissionLetterInStatus}
        </if>
        <!--教育局审核员-->
        <if test="search.schoolIds != null and search.schoolIds != ''">
            AND sb.enroll_school_id in
            <foreach collection="search.schoolIds" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>


    </select>

    <select id="details" resultType="com.shida.region.biz.pojo.vo.RecruitStudentDetailsVo">
        SELECT
            /*sb.id AS student_base_id,*/
            sb.enroll_id,
            sb.enroll_school_name,
            sb.adjust_school_name,
            sb.school_review_reason,
            sb.school_username,
            sb.school_operation_time,
            sb.education_review_reason,
            sb.education_username,
            sb.education_operation_time,
            sb.house_info_type,

            sbi.student_name,
            sbi.student_id_card_number,
            sbi.student_brith_date,
            sbi.student_gender,
            sbi.student_nation,
            sbi.student_current_address,
            sbi.student_residence_address,
            sbi.child_vaccination_certificate,
            sbi.birth_certificate,

            ri.student_registration_page,
            ri.guardian2_registration_first_page,
            ri.guardian1_registration_page,
            ri.guardian2_registration_first_page,
            ri.guardian2_registration_page,

            gi.marriage_certificate,
            gi.guardian1_name,
            gi.guardian1_current_address,
            gi.guardian1_relation,
            gi.guardian1_phone,
            gi.guardian1_residence_address,
            gi.guardian2_name,
            gi.guardian2_current_address,
            gi.guardian2_relation,
            gi.guardian2_phone,
            gi.guardian2_residence_address,
            gi.guardian1_id_card,
            gi.guardian2_id_card

        FROM r_student_base AS sb
                 LEFT JOIN r_student_base_info AS sbi ON sb.id = sbi.student_id
                 LEFT JOIN r_guardian_info AS gi ON sb.id = gi.student_id
                 LEFT JOIN r_registration_info AS ri ON sb.id = ri.student_id
        WHERE sb.delete_flag = 0 AND sb.adjustment = 0
          AND sb.id = #{id}
    </select>


    <select id="getList" resultType="com.shida.region.biz.pojo.vo.RecruitStudentVo">
        SELECT
        sb.id AS student_base_id,
        sb.id AS studentId,
        sb.enroll_id,sb.enroll_time,sb.enroll_school_name,sb.school_review_status,
        sb.school_review_reason,sb.education_review_status,sb.education_review_reason,sb.estate_review_status,
        sb.estate_review_reason,sb.public_security_review_status,sb.public_security_review_reason,sb.check_in_status,
        sb.house_info_type,sb.adjust_school_id,sb.adjust_school_name,sb.school_username,sb.school_operation_time,
        sb.education_username,sb.education_operation_time,
        sbi.enroll_number,sbi.student_name,sbi.student_id_card_number,sbi.student_brith_date,
        sbi.student_gender,sbi.student_nation,sbi.student_residence_address,
        sbi.student_current_address,sbi.child_vaccination_certificate,sbi.birth_certificate,
        save.ids_name AS type,
        sb.set_up_save_ids,
        sbi.reserve_field1 AS is_entitled_group,
        sbi.reserve_field2 AS entitled_group_type,
        sb.publicity_status,
        sb.un_check_in_reason,
        sb.admission_letter_in_status,
        rgi.guardian1_name,
        rgi.guardian1_phone,
        td.nature
        FROM r_student_base AS sb
        LEFT JOIN r_student_base_info AS sbi ON sb.id = sbi.student_id
        LEFT JOIN r_guardian_info AS rgi ON sb.id = rgi.student_id
        LEFT JOIN t_registration_system_set_up_sava AS save ON sb.set_up_save_ids = save.setup_id
        LEFT JOIN `${dbName}`.t_dept AS td ON sb.enroll_school_id = td.id
        WHERE sb.delete_flag = 0 AND sb.adjustment = 0
        <!--报名阶段-->
        <if test="search.enrollStage != null and search.enrollStage != ''">
            AND sb.enroll_stage = #{search.enrollStage}
        </if>
        <!--是否公示(0未公示 1 已公示)-->
        <if test="search.publicityStatus != null and search.publicityStatus != ''">
            AND sb.publicity_status = #{search.publicityStatus}
        </if>
        <!--报名ID-->
        <if test="search.enrollId != null and search.enrollId != ''">
            AND sb.enroll_id like CONCAT('%',#{search.enrollId},'%')
        </if>
        <!--姓名或者身份证-->
        <if test="search.studentName != null and search.studentName != ''">
            AND (sbi.student_name like CONCAT('%',#{search.studentName},'%') or
            sbi.student_id_card_number like CONCAT('%',#{search.studentName},'%'))
        </if>
        <!--学校性质-->
        <if test="search.nature != null and search.nature != ''">
            AND td.nature = #{search.nature}
        </if>
        <!--报名开始时间,结束时间-->
        <if test="search.createdTime != null">
            AND sb.enroll_time &gt;= #{search.createdTime}
        </if>
        <if test="search.endTime != null">
            AND sb.enroll_time &lt;= #{search.endTime}
        </if>
        <!--学校审核状态-->
        <if test="search.schoolReviewStatus != null and search.schoolReviewStatus != ''">
            AND sb.school_review_status = #{search.schoolReviewStatus}
        </if>
        <!--教育局审核状态-->
        <if test="search.educationReviewStatus and search.educationReviewStatus != ''">
            AND sb.education_review_status = #{search.educationReviewStatus}
        </if>
        <!--公安审核状态-->
        <if test="search.publicSecurityReviewStatus != null and search.publicSecurityReviewStatus != ''">
            AND sb.public_security_review_status = #{search.publicSecurityReviewStatus}
        </if>
        <!--房产审核状态-->
        <if test="search.estateReviewStatus != null and search.estateReviewStatus != ''">
            AND sb.estate_review_status = #{search.estateReviewStatus}
        </if>
        <!--是否报道-->
        <if test="search.checkInStatus != null ">
            AND sb.check_in_status = #{search.checkInStatus}
        </if>
        <!--是否发送录取通知书-->
        <if test="search.admissionLetterInStatus != null ">
            AND sb.admission_letter_in_status = #{search.admissionLetterInStatus}
        </if>
        <!--类别-->
        <if test="search.type != null and search.type != ''">
            <!-- AND save.setup_id = #{search.type}-->
            AND save.ids_name like CONCAT('%',#{search.type},'%')
        </if>
        <!--调剂学校名称-->
        <if test="search.adjustSchoolName != null and search.adjustSchoolName != ''">
            AND sb.adjust_school_name like CONCAT('%',#{search.adjustSchoolName},'%')
        </if>
        <choose>
            <!--如果是学校账号
             OR (sb.adjust_school_id = #{search.enrollSchoolId})-->
            <when test="search.role != null and search.role == 'SCHOOL'">
                AND (
                (sb.enroll_school_id = #{search.enrollSchoolId} AND sb.adjust_school_id IS NULL )

                )
            </when>
            <otherwise>
                <!--学校ID-->
                <!--报名学校是A 调剂到B 那么 搜索条件是A 搜不到 搜索条件是B 搜不到，只有搜索条件是调剂学校才能搜到-->
                <if test="search.enrollSchoolId != null and search.enrollSchoolId != ''
                    and search.adjustSchoolId == null or search.adjustSchoolId == '' ">
                    AND sb.enroll_school_id = #{search.enrollSchoolId}
                    AND sb.adjust_school_id IS NULL
                </if>
                <!--调剂学校ID-->
                <if test="search.adjustSchoolId != null and search.adjustSchoolId != ''
                    and search.enrollSchoolId == null or search.enrollSchoolId == ''">
                    AND sb.adjust_school_id = #{search.adjustSchoolId}
                </if>
                <!--报名学校ID 调剂学校ID-->
                <if test="search.enrollSchoolId != null and search.enrollSchoolId != ''
                    and search.adjustSchoolId != null and search.adjustSchoolId != '' ">
                    AND sb.enroll_school_id = #{search.enrollSchoolId}
                    AND sb.adjust_school_id = #{search.adjustSchoolId}
                </if>
            </otherwise>
        </choose>

    </select>

    <select id="getLzExportStudent" resultType="com.shida.region.biz.pojo.excel.LzExportStudentExcel">
        SELECT
        sb.id AS student_id,
        sb.set_up_save_ids ,
        sb.school_review_status AS schoolReviewStatus,
        sb.education_review_status AS educationReviewStatus,
        sb.public_security_review_status AS publicSecurityReviewStatus,
        sb.estate_review_status AS estateReviewStatus,
        sb.enroll_id,
        sb.house_info_type,
        sb.enroll_school_name AS enrollSchoolName,
        sbi.student_name,
        sbi.student_brith_date,
        sbi.student_id_card_number,
        sbi.is_disabled,
        sbi.student_gender,
        sbi.student_nation,
        sbi.student_residence_address,
        sbi.reserve_field2 AS studentReserveField2,
        sbi.child_vaccination_certificate,
        sbi.birth_certificate,
        sbi.graduate_primary_school,
        save.ids_name AS type,
        sb.set_up_save_ids,
        sbi.reserve_field1,
        sb.publicity_status,
        sb.un_check_in_reason,
        sb.admission_letter_in_status,
        td.nature,
        rgi.guardian1_name,
        rgi.guardian1_id_card_number,
        rgi.guardian1_relation,
        rgi.guardian1_phone,
        rgi.guardian1_residence_address,
        rgi.guardian2_name,
        rgi.guardian2_id_card_number,
        rgi.guardian2_relation,
        rgi.guardian2_phone,
        rgi.guardian2_residence_address,
        rri.household_name,
        rri.household_id_card_number,
        rri.account_number,
        rri.relation_of_student AS relationOfStudent,
        rri.registration_address,
        rri.reserve_field1 AS huReserveField1,
        rri2.residence_permit_number,
        rri2.residence_permit_name,
        rri2.id_card_number AS residenceIdCardNumber,
        rri2.residence_permit_address,
        rri2.relation_of_student AS residenceRelationOfStudent,
        rri2.reserve_field1 AS juzhuReserveField1,
        rwi.contract_number AS workUnit,
        rwi.company_name AS workCompanyName,
        rwi.address AS workAddress,
        rwi.contract_number AS workContractNumber,
        rwi.employee_name AS employeeName,
        rwi.relation_of_student AS workRelationOfStudent,
        rbi.company_name AS companyName,
        rbi.operator_name AS operatorName,
        rbi.registration_time AS registrationTime,
        rbi.reserve_field2 AS reserveField2,
        rbi.relation_of_student AS commodityRelationOfStudent
        FROM
        r_student_base AS sb
        LEFT JOIN r_student_base_info AS sbi ON sb.id = sbi.student_id
        LEFT JOIN r_guardian_info AS rgi ON sb.id = rgi.student_id
        LEFT JOIN r_registration_info AS rri ON sb.id = rri.student_id
        LEFT JOIN r_residence_info AS rri2 ON sb.id = rri2.student_id
        LEFT JOIN r_work_info AS rwi ON sb.id = rwi.student_id
        LEFT JOIN r_business_info AS rbi ON sb.id = rbi.student_id
        LEFT JOIN t_registration_system_set_up_sava AS save ON sb.set_up_save_ids = save.setup_id
        LEFT JOIN `${dbName}`.t_dept AS td ON sb.enroll_school_id = td.id
        WHERE sb.delete_flag = 0 AND sb.adjustment = 0
        <!--报名阶段-->
        <if test="search.enrollStage != null and search.enrollStage != ''">
            AND sb.enroll_stage = #{search.enrollStage}
        </if>
        <!--是否公示(0未公示 1 已公示)-->
        <if test="search.publicityStatus != null and search.publicityStatus != ''">
            AND sb.publicity_status = #{search.publicityStatus}
        </if>
        <!--报名ID-->
        <if test="search.enrollId != null and search.enrollId != ''">
            AND sb.enroll_id like CONCAT('%',#{search.enrollId},'%')
        </if>
        <!--姓名或者身份证-->
        <if test="search.studentName != null and search.studentName != ''">
            AND (sbi.student_name like CONCAT('%',#{search.studentName},'%') or
            sbi.student_id_card_number like CONCAT('%',#{search.studentName},'%'))
        </if>
        <!--学校性质-->
        <if test="search.nature != null and search.nature != ''">
            AND td.nature = #{search.nature}
        </if>
        <!--报名开始时间,结束时间-->
        <if test="search.createdTime != null">
            AND sb.enroll_time &gt;= #{search.createdTime}
        </if>
        <if test="search.endTime != null">
            AND sb.enroll_time &lt;= #{search.endTime}
        </if>
        <!--学校审核状态-->
        <if test="search.schoolReviewStatus != null and search.schoolReviewStatus != ''">
            AND sb.school_review_status = #{search.schoolReviewStatus}
        </if>
        <!--教育局审核状态-->
        <if test="search.educationReviewStatus and search.educationReviewStatus != ''">
            AND sb.education_review_status = #{search.educationReviewStatus}
        </if>
        <!--公安审核状态-->
        <if test="search.publicSecurityReviewStatus != null and search.publicSecurityReviewStatus != ''">
            AND sb.public_security_review_status = #{search.publicSecurityReviewStatus}
        </if>
        <!--房产审核状态-->
        <if test="search.estateReviewStatus != null and search.estateReviewStatus != ''">
            AND sb.estate_review_status = #{search.estateReviewStatus}
        </if>
        <!--是否报道-->
        <if test="search.checkInStatus != null   ">
            AND sb.check_in_status = #{search.checkInStatus}
        </if>
        <!--是否发送录取通知书-->
        <if test="search.admissionLetterInStatus != null ">
            AND sb.admission_letter_in_status = #{search.admissionLetterInStatus}
        </if>
        <!--类别-->
        <if test="search.type != null and search.type != ''">
            AND save.setup_id = #{search.type}
        </if>
        <!--调剂学校名称-->
        <if test="search.adjustSchoolName != null and search.adjustSchoolName != ''">
            AND sb.adjust_school_name like CONCAT('%',#{search.adjustSchoolName},'%')
        </if>
        <choose>
            <!--如果是学校账号
             OR (sb.adjust_school_id = #{search.enrollSchoolId})-->
            <when test="search.role != null and search.role == 'SCHOOL'">
                AND (
                (sb.enroll_school_id = #{search.enrollSchoolId} AND sb.adjust_school_id IS NULL )

                )
            </when>
            <otherwise>
                <!--学校ID-->
                <!--报名学校是A 调剂到B 那么 搜索条件是A 搜不到 搜索条件是B 搜不到，只有搜索条件是调剂学校才能搜到-->
                <if test="search.enrollSchoolId != null and search.enrollSchoolId != ''
                    and search.adjustSchoolId == null or search.adjustSchoolId == '' ">
                    AND sb.enroll_school_id = #{search.enrollSchoolId}
                    AND sb.adjust_school_id IS NULL
                </if>
                <!--调剂学校ID-->
                <if test="search.adjustSchoolId != null and search.adjustSchoolId != ''
                    and search.enrollSchoolId == null or search.enrollSchoolId == ''">
                    AND sb.adjust_school_id = #{search.adjustSchoolId}
                </if>
                <!--报名学校ID 调剂学校ID-->
                <if test="search.enrollSchoolId != null and search.enrollSchoolId != ''
                    and search.adjustSchoolId != null and search.adjustSchoolId != '' ">
                    AND sb.enroll_school_id = #{search.enrollSchoolId}
                    AND sb.adjust_school_id = #{search.adjustSchoolId}
                </if>
            </otherwise>
        </choose>
    </select>

    <select id="getLzJuniorSchoolExportStudent" resultType="com.shida.region.biz.pojo.excel.LzJuniorSchoolExportStudentExcel">
        SELECT
        sb.id AS student_id,
        sb.set_up_save_ids ,
        sb.school_review_status AS schoolReviewStatus,
        sb.education_review_status AS educationReviewStatus,
        sb.public_security_review_status AS publicSecurityReviewStatus,
        sb.estate_review_status AS estateReviewStatus,
        sb.enroll_id,
        sb.house_info_type,
        sb.enroll_school_name AS enrollSchoolName,
        sbi.student_name,
        sbi.student_brith_date,
        sbi.student_id_card_number,
        sbi.is_disabled,
        sbi.student_gender,
        sbi.student_nation,
        sbi.student_residence_address,
        sbi.reserve_field2 AS studentReserveField2,
        sbi.child_vaccination_certificate,
        sbi.birth_certificate,
        sbi.graduate_primary_school,
        save.ids_name AS type,
        sb.set_up_save_ids,
        sbi.reserve_field1,
        sb.publicity_status,
        sb.un_check_in_reason,
        sb.admission_letter_in_status,
        td.nature,
        rgi.guardian1_name,
        rgi.guardian1_id_card_number,
        rgi.guardian1_relation,
        rgi.guardian1_phone,
        rgi.guardian1_residence_address,
        rgi.guardian2_name,
        rgi.guardian2_id_card_number,
        rgi.guardian2_relation,
        rgi.guardian2_phone,
        rgi.guardian2_residence_address,
        rri.household_name,
        rri.household_id_card_number,
        rri.account_number,
        rri.relation_of_student AS relationOfStudent,
        rri.registration_address,
        rri.reserve_field1 AS huReserveField1,
        rri2.residence_permit_number,
        rri2.residence_permit_name,
        rri2.id_card_number AS residenceIdCardNumber,
        rri2.residence_permit_address,
        rri2.relation_of_student AS residenceRelationOfStudent,
        rri2.reserve_field1 AS juzhuReserveField1,
        rwi.contract_number AS workUnit,
        rwi.company_name AS workCompanyName,
        rwi.address AS workAddress,
        rwi.contract_number AS workContractNumber,
        rwi.employee_name AS employeeName,
        rwi.relation_of_student AS workRelationOfStudent,
        rwi.reserve_field2 AS workRelationReserveField2,
        rbi.company_name AS companyName,
        rbi.operator_name AS operatorName,
        rbi.registration_time AS registrationTime,
        rbi.reserve_field2 AS reserveField2,
        rbi.relation_of_student AS commodityRelationOfStudent
        FROM
        r_student_base AS sb
        LEFT JOIN r_student_base_info AS sbi ON sb.id = sbi.student_id
        LEFT JOIN r_guardian_info AS rgi ON sb.id = rgi.student_id
        LEFT JOIN r_registration_info AS rri ON sb.id = rri.student_id
        LEFT JOIN r_residence_info AS rri2 ON sb.id = rri2.student_id
        LEFT JOIN r_work_info AS rwi ON sb.id = rwi.student_id
        LEFT JOIN r_business_info AS rbi ON sb.id = rbi.student_id
        LEFT JOIN t_registration_system_set_up_sava AS save ON sb.set_up_save_ids = save.setup_id
        LEFT JOIN `${dbName}`.t_dept AS td ON sb.enroll_school_id = td.id
        WHERE sb.delete_flag = 0 AND sb.adjustment = 0
        <!--报名阶段-->
        <if test="search.enrollStage != null and search.enrollStage != ''">
            AND sb.enroll_stage = #{search.enrollStage}
        </if>
        <!--是否公示(0未公示 1 已公示)-->
        <if test="search.publicityStatus != null and search.publicityStatus != ''">
            AND sb.publicity_status = #{search.publicityStatus}
        </if>
        <!--报名ID-->
        <if test="search.enrollId != null and search.enrollId != ''">
            AND sb.enroll_id like CONCAT('%',#{search.enrollId},'%')
        </if>
        <!--姓名或者身份证-->
        <if test="search.studentName != null and search.studentName != ''">
            AND (sbi.student_name like CONCAT('%',#{search.studentName},'%') or
            sbi.student_id_card_number like CONCAT('%',#{search.studentName},'%'))
        </if>
        <!--学校性质-->
        <if test="search.nature != null and search.nature != ''">
            AND td.nature = #{search.nature}
        </if>
        <!--报名开始时间,结束时间-->
        <if test="search.createdTime != null">
            AND sb.enroll_time &gt;= #{search.createdTime}
        </if>
        <if test="search.endTime != null">
            AND sb.enroll_time &lt;= #{search.endTime}
        </if>
        <!--学校审核状态-->
        <if test="search.schoolReviewStatus != null and search.schoolReviewStatus != ''">
            AND sb.school_review_status = #{search.schoolReviewStatus}
        </if>
        <!--教育局审核状态-->
        <if test="search.educationReviewStatus and search.educationReviewStatus != ''">
            AND sb.education_review_status = #{search.educationReviewStatus}
        </if>
        <!--公安审核状态-->
        <if test="search.publicSecurityReviewStatus != null and search.publicSecurityReviewStatus != ''">
            AND sb.public_security_review_status = #{search.publicSecurityReviewStatus}
        </if>
        <!--房产审核状态-->
        <if test="search.estateReviewStatus != null and search.estateReviewStatus != ''">
            AND sb.estate_review_status = #{search.estateReviewStatus}
        </if>
        <!--是否报道-->
        <if test="search.checkInStatus != null   ">
            AND sb.check_in_status = #{search.checkInStatus}
        </if>
        <!--是否发送录取通知书-->
        <if test="search.admissionLetterInStatus != null ">
            AND sb.admission_letter_in_status = #{search.admissionLetterInStatus}
        </if>
        <!--类别-->
        <if test="search.type != null and search.type != ''">
            AND save.setup_id = #{search.type}
        </if>
        <!--调剂学校名称-->
        <if test="search.adjustSchoolName != null and search.adjustSchoolName != ''">
            AND sb.adjust_school_name like CONCAT('%',#{search.adjustSchoolName},'%')
        </if>
        <choose>
            <!--如果是学校账号
             OR (sb.adjust_school_id = #{search.enrollSchoolId})-->
            <when test="search.role != null and search.role == 'SCHOOL'">
                AND (
                (sb.enroll_school_id = #{search.enrollSchoolId} AND sb.adjust_school_id IS NULL )

                )
            </when>
            <otherwise>
                <!--学校ID-->
                <!--报名学校是A 调剂到B 那么 搜索条件是A 搜不到 搜索条件是B 搜不到，只有搜索条件是调剂学校才能搜到-->
                <if test="search.enrollSchoolId != null and search.enrollSchoolId != ''
                    and search.adjustSchoolId == null or search.adjustSchoolId == '' ">
                    AND sb.enroll_school_id = #{search.enrollSchoolId}
                    AND sb.adjust_school_id IS NULL
                </if>
                <!--调剂学校ID-->
                <if test="search.adjustSchoolId != null and search.adjustSchoolId != ''
                    and search.enrollSchoolId == null or search.enrollSchoolId == ''">
                    AND sb.adjust_school_id = #{search.adjustSchoolId}
                </if>
                <!--报名学校ID 调剂学校ID-->
                <if test="search.enrollSchoolId != null and search.enrollSchoolId != ''
                    and search.adjustSchoolId != null and search.adjustSchoolId != '' ">
                    AND sb.enroll_school_id = #{search.enrollSchoolId}
                    AND sb.adjust_school_id = #{search.adjustSchoolId}
                </if>
            </otherwise>
        </choose>
    </select>


    <select id="getWaczExportStudent" resultType="com.shida.region.biz.pojo.excel.WaczExportStudentExcel">
        SELECT
        sb.id AS student_id,
        sb.school_review_status AS schoolReviewStatus,
        sb.education_review_status AS educationReviewStatus,
        sb.public_security_review_status AS publicSecurityReviewStatus,
        sb.estate_review_status AS estateReviewStatus,
        sb.enroll_id,
        sb.house_info_type,
        sb.enroll_school_name AS enrollSchoolName,
        sbi.student_name,
        sbi.student_current_address,
        sbi.student_brith_date,
        sbi.student_id_card_number,
        sbi.is_disabled,
        sbi.student_gender,
        sbi.student_nation,
        sbi.student_residence_address,
        sbi.reserve_field2 AS studentReserveField2,
        sbi.child_vaccination_certificate,
        sbi.birth_certificate,
        sbi.graduate_primary_school,
        save.ids_name AS type,
        sb.set_up_save_ids,
        sbi.reserve_field1,
        sb.publicity_status,
        sb.un_check_in_reason,
        sb.admission_letter_in_status,
        td.nature,
        rgi.guardian1_name,
        rgi.guardian1_id_card_number,
        rgi.guardian1_relation,
        rgi.guardian1_phone,
        rgi.guardian1_residence_address,
        rgi.guardian2_name,
        rgi.guardian2_id_card_number,
        rgi.guardian2_relation,
        rgi.guardian2_phone,
        rgi.guardian2_residence_address,
        rri.household_name,
        rri.household_id_card_number,
        rri.account_number,
        rri.relation_of_student AS relationOfStudent,
        rri.registration_address,
        rri.reserve_field1 AS huReserveField1,
        rri2.residence_permit_number,
        rri2.residence_permit_name,
        rri2.id_card_number AS residenceIdCardNumber,
        rri2.residence_permit_address,
        rri2.relation_of_student AS residenceRelationOfStudent,
        rri2.reserve_field1 AS juzhuReserveField1,

        rri2.residence_permit_number AS residencePermitNumber,
        rri2.reserve_field1 AS residenceReserveField1,
        rri2.reserve_field2 AS residenceReserveField2,

        rwi.contract_number AS workUnit,
        rwi.company_name AS workCompanyName,
        rwi.address AS workAddress,
        rwi.contract_number AS workContractNumber,
        rwi.employee_name AS employeeName,
        rwi.relation_of_student AS workRelationOfStudent,

        rwi.sign_time AS signTime,
        rbi.credit_code AS creditCode,
        rbi.address AS creditAddress,

        rbi.company_name AS companyName,
        rbi.operator_name AS operatorName,
        rbi.registration_time AS registrationTime,
        rbi.reserve_field2 AS reserveField2,
        rbi.relation_of_student AS commodityRelationOfStudent
        FROM
        r_student_base AS sb
        LEFT JOIN r_student_base_info AS sbi ON sb.id = sbi.student_id
        LEFT JOIN r_guardian_info AS rgi ON sb.id = rgi.student_id
        LEFT JOIN r_registration_info AS rri ON sb.id = rri.student_id
        LEFT JOIN r_residence_info AS rri2 ON sb.id = rri2.student_id
        LEFT JOIN r_work_info AS rwi ON sb.id = rwi.student_id
        LEFT JOIN r_business_info AS rbi ON sb.id = rbi.student_id
        LEFT JOIN t_registration_system_set_up_sava AS save ON sb.set_up_save_ids = save.setup_id
        LEFT JOIN `${dbName}`.t_dept AS td ON sb.enroll_school_id = td.id
        WHERE sb.delete_flag = 0 AND sb.adjustment = 0
        <!--报名阶段-->
        <if test="search.enrollStage != null and search.enrollStage != ''">
            AND sb.enroll_stage = #{search.enrollStage}
        </if>
        <!--是否公示(0未公示 1 已公示)-->
        <if test="search.publicityStatus != null and search.publicityStatus != ''">
            AND sb.publicity_status = #{search.publicityStatus}
        </if>
        <!--报名ID-->
        <if test="search.enrollId != null and search.enrollId != ''">
            AND sb.enroll_id like CONCAT('%',#{search.enrollId},'%')
        </if>
        <!--姓名或者身份证-->
        <if test="search.studentName != null and search.studentName != ''">
            AND (sbi.student_name like CONCAT('%',#{search.studentName},'%') or
            sbi.student_id_card_number like CONCAT('%',#{search.studentName},'%'))
        </if>
        <!--学校性质-->
        <if test="search.nature != null and search.nature != ''">
            AND td.nature = #{search.nature}
        </if>
        <!--报名开始时间,结束时间-->
        <if test="search.createdTime != null">
            AND sb.enroll_time &gt;= #{search.createdTime}
        </if>
        <if test="search.endTime != null">
            AND sb.enroll_time &lt;= #{search.endTime}
        </if>
        <!--学校审核状态-->
        <if test="search.schoolReviewStatus != null and search.schoolReviewStatus != ''">
            AND sb.school_review_status = #{search.schoolReviewStatus}
        </if>
        <!--教育局审核状态-->
        <if test="search.educationReviewStatus and search.educationReviewStatus != ''">
            AND sb.education_review_status = #{search.educationReviewStatus}
        </if>
        <!--公安审核状态-->
        <if test="search.publicSecurityReviewStatus != null and search.publicSecurityReviewStatus != ''">
            AND sb.public_security_review_status = #{search.publicSecurityReviewStatus}
        </if>
        <!--房产审核状态-->
        <if test="search.estateReviewStatus != null and search.estateReviewStatus != ''">
            AND sb.estate_review_status = #{search.estateReviewStatus}
        </if>
        <!--是否报道-->
        <if test="search.checkInStatus != null ">
            AND sb.check_in_status = #{search.checkInStatus}
        </if>
        <!--是否发送录取通知书-->
        <if test="search.admissionLetterInStatus != null ">
            AND sb.admission_letter_in_status = #{search.admissionLetterInStatus}
        </if>
        <!--类别-->
        <if test="search.type != null and search.type != ''">
            AND save.setup_id = #{search.type}
        </if>
        <!--调剂学校名称-->
        <if test="search.adjustSchoolName != null and search.adjustSchoolName != ''">
            AND sb.adjust_school_name like CONCAT('%',#{search.adjustSchoolName},'%')
        </if>
        <choose>
            <!--如果是学校账号
             OR (sb.adjust_school_id = #{search.enrollSchoolId})-->
            <when test="search.role != null and search.role == 'SCHOOL'">
                AND (
                (sb.enroll_school_id = #{search.enrollSchoolId} AND sb.adjust_school_id IS NULL )

                )
            </when>
            <otherwise>
                <!--学校ID-->
                <!--报名学校是A 调剂到B 那么 搜索条件是A 搜不到 搜索条件是B 搜不到，只有搜索条件是调剂学校才能搜到-->
                <if test="search.enrollSchoolId != null and search.enrollSchoolId != ''
                    and search.adjustSchoolId == null or search.adjustSchoolId == '' ">
                    AND sb.enroll_school_id = #{search.enrollSchoolId}
                    AND sb.adjust_school_id IS NULL
                </if>
                <!--调剂学校ID-->
                <if test="search.adjustSchoolId != null and search.adjustSchoolId != ''
                    and search.enrollSchoolId == null or search.enrollSchoolId == ''">
                    AND sb.adjust_school_id = #{search.adjustSchoolId}
                </if>
                <!--报名学校ID 调剂学校ID-->
                <if test="search.enrollSchoolId != null and search.enrollSchoolId != ''
                    and search.adjustSchoolId != null and search.adjustSchoolId != '' ">
                    AND sb.enroll_school_id = #{search.enrollSchoolId}
                    AND sb.adjust_school_id = #{search.adjustSchoolId}
                </if>
            </otherwise>
        </choose>
    </select>

    <select id="ceshi" resultType="com.shida.region.biz.pojo.vo.RecruitStudentVo">
        SELECT sb.id              AS student_base_id,
               sb.enroll_id,
               sb.enroll_time,
               sb.enroll_school_name,
               sb.school_review_status,
               sb.school_review_reason,
               sb.education_review_status,
               sb.education_review_reason,
               sb.estate_review_status,
               sb.estate_review_reason,
               sb.public_security_review_status,
               sb.public_security_review_reason,
               sb.check_in_status,
               sb.house_info_type,
               sb.adjust_school_id,
               sb.adjust_school_name,
               sb.school_username,
               sb.school_operation_time,
               sb.education_username,
               sb.education_operation_time,
               sbi.enroll_number,
               sbi.student_name,
               sbi.student_id_card_number,
               sbi.student_brith_date,
               sbi.student_gender,
               sbi.student_nation,
               sbi.student_residence_address,
               sbi.student_current_address,
               sbi.child_vaccination_certificate,
               sbi.birth_certificate,
               save.ids_name      AS type,
               sb.set_up_save_ids,
               sbi.reserve_field1 AS is_entitled_group,
               sbi.reserve_field2 AS entitled_group_type,
               sb.publicity_status,
               sb.un_check_in_reason,
               td.nature
        FROM r_student_base AS sb
                 LEFT JOIN r_student_base_info AS sbi ON sb.id = sbi.student_id
                 LEFT JOIN t_registration_system_set_up_sava AS save ON sb.set_up_save_ids = save.setup_id
                 LEFT JOIN `${dbName}`.t_dept AS td ON sb.enroll_school_id = td.id
        WHERE sb.delete_flag = 0
          AND sb.id = #{id}
    </select>

    <select id="selectDeleteEnrollStudentInfo"
            resultType="com.shida.region.biz.pojo.vo.ClearEnrollStudentInfoVo">
        SELECT rsb.id, rsbi.student_name, rsb.enroll_school_name
        FROM r_student_base rsb
                 LEFT JOIN r_student_base_info rsbi
                           ON rsb.id = rsbi.student_id
        WHERE rsb.delete_flag = 0
          AND rsbi.delete_flag = 0
          AND rsbi.student_id_card_number = #{idCardNumber,jdbcType=VARCHAR}
    </select>
    <select id="selectClearStudentList" resultType="com.shida.region.biz.pojo.vo.ClearEnrollStudentInfoVo">
        SELECT rsb.id,
        rsbi.student_name,
        rsb.enroll_school_name,
        rsbi.student_id_card_number,
        rsb.update_time,
        rsb.user_id
        FROM r_student_base rsb
        LEFT JOIN r_student_base_info rsbi
        ON rsb.id = rsbi.student_id
        WHERE rsb.clear_flag = 1  AND rsb.adjustment = 0
        <if test="idCardNumber != '' and idCardNumber != null">
            AND rsbi.student_id_card_number = #{idCardNumber,jdbcType=VARCHAR}
        </if>
        AND rsb.delete_flag = 1
        AND rsbi.delete_flag = 1
    </select>


    <select id="getStudentInFo" resultType="com.shida.region.biz.pojo.vo.StudentInFoVo">
        SELECT sbi.student_name,
               sbi.student_id_card_number,
               sbi.student_gender,
               sbi.student_current_address,
               sb.enroll_id,
               sb.enroll_school_name,
               sb.house_info_type,
               ts.ids_name,
               gi.guardian1_name,
               gi.guardian1_id_card_number,
               gi.guardian1_relation,
               gi.guardian1_phone,
               gi.guardian2_name,
               gi.guardian2_relation,
               gi.guardian2_phone,
               gi.guardian2_id_card_number,
               ri.household_name,
               ri.household_id_card_number,
               ri.account_number,
               ri.relation_of_student
        FROM r_student_base_info AS sbi
                 LEFT JOIN r_student_base AS sb ON sbi.student_id = sb.id
                 LEFT JOIN t_registration_system_set_up_sava AS ts ON sb.set_up_save_ids = ts.setup_id
                 LEFT JOIN r_guardian_info AS gi ON sb.id = gi.student_id
                 LEFT JOIN r_registration_info AS ri ON ri.student_id = sb.id
        WHERE sb.id = #{id}
    </select>


    <!-- or id = #{item} -->
    <select id="batchExportHouseholdSurvey" resultType="com.shida.region.biz.pojo.vo.BatchExportHouseholdSurveyVo">
        SELECT r_student_base.id,
        r_student_base.path,
        r_student_base_info.student_name,
        r_student_base_info.student_id_card_number
        FROM r_student_base
        LEFT JOIN r_student_base_info ON r_student_base.id = r_student_base_info.student_id
        WHERE r_student_base.id in
        <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>

    </select>

    <select id="getSearchList" resultType="java.lang.Long">
        SELECT
        sb.id
        FROM r_student_base AS sb
        LEFT JOIN r_student_base_info AS sbi ON sb.id = sbi.student_id
        LEFT JOIN t_registration_system_set_up_sava AS save ON sb.set_up_save_ids = save.setup_id
        LEFT JOIN `${dbName}`.t_dept AS td ON sb.enroll_school_id = td.id
        WHERE sb.delete_flag = 0  AND sb.adjustment = 0
        <!--报名阶段-->
        <if test="search.enrollStage != null and search.enrollStage != ''">
            AND sb.enroll_stage = #{search.enrollStage}
        </if>
        <!--姓名或者身份证-->
        <if test="search.studentName != null and search.studentName != ''">
            AND (sbi.student_name like CONCAT('%',#{search.studentName},'%') or
            sbi.student_id_card_number like CONCAT('%',#{search.studentName},'%'))
        </if>
        <!--是否公示(0未公示 1 已公示)-->
        <if test="search.publicityStatus != null and search.publicityStatus != ''">
            AND sb.publicity_status = #{search.publicityStatus}
        </if>
        <!--报名ID-->
        <if test="search.enrollId != null and search.enrollId != ''">
            AND sb.enroll_id like CONCAT('%',#{search.enrollId},'%')
        </if>
        <!--学校名称-->
        <if test="search.enrollSchoolName != null and search.enrollSchoolName != ''">
            AND sb.enroll_school_name like CONCAT('%',#{search.enrollSchoolName},'%')
        </if>
        <!--调剂学校名称-->
        <if test="search.adjustSchoolName != null and search.adjustSchoolName != ''">
            AND sb.adjust_school_name like CONCAT('%',#{search.adjustSchoolName},'%')
        </if>
        <!--学校性质-->
        <if test="search.nature != null and search.nature != ''">
            AND td.nature = #{search.nature}
        </if>
        <!--类别-->
        <if test="search.type != null and search.type != ''">
            AND save.ids_name like CONCAT('%',#{search.type},'%')
        </if>
        <!--报名开始时间,结束时间-->
        <if test="search.createdTime != null">
            AND sb.enroll_time &gt;= #{search.createdTime}
        </if>
        <if test="search.endTime != null">
            AND sb.enroll_time &lt;= #{search.endTime}
        </if>
        <!--学校审核状态-->
        <if test="search.schoolReviewStatus != null and search.schoolReviewStatus != ''">
            AND sb.school_review_status = #{search.schoolReviewStatus}
        </if>
        <!--教育局审核状态-->
        <if test="search.educationReviewStatus and search.educationReviewStatus != ''">
            AND sb.education_review_status = #{search.educationReviewStatus}
        </if>
        <!--公安审核状态-->
        <if test="search.publicSecurityReviewStatus != null and search.publicSecurityReviewStatus != ''">
            AND sb.public_security_review_status = #{search.publicSecurityReviewStatus}
        </if>
        <!--房产审核状态-->
        <if test="search.estateReviewStatus != null and search.estateReviewStatus != ''">
            AND sb.estate_review_status = #{search.estateReviewStatus}
        </if>
        <!--是否发送录取通知书-->
        <if test="search.admissionLetterInStatus != null ">
            AND sb.admission_letter_in_status = #{search.admissionLetterInStatus}
        </if>
        <!--是否报道-->
        <if test="search.checkInStatus != null ">
            AND sb.check_in_status = #{search.checkInStatus}
        </if>
        <choose>
            <!--如果是学校账号
             OR (sb.adjust_school_id = #{search.enrollSchoolId})-->
            <when test="search.role != null and search.role == 'SCHOOL'">
                AND (
                (sb.enroll_school_id = #{search.enrollSchoolId} AND sb.adjust_school_id IS NULL )

                )
            </when>
            <otherwise>
                <!--学校ID-->
                <!--报名学校是A 调剂到B 那么 搜索条件是A 搜不到 搜索条件是B 搜不到，只有搜索条件是调剂学校才能搜到-->
                <if test="search.enrollSchoolId != null and search.enrollSchoolId != ''
                    and search.adjustSchoolId == null or search.adjustSchoolId == '' ">
                    AND sb.enroll_school_id = #{search.enrollSchoolId}
                    AND sb.adjust_school_id IS NULL
                </if>
                <!--调剂学校ID-->
                <if test="search.adjustSchoolId != null and search.adjustSchoolId != ''
                    and search.enrollSchoolId == null or search.enrollSchoolId == ''">
                    AND sb.adjust_school_id = #{search.adjustSchoolId}
                </if>
                <!--报名学校ID 调剂学校ID-->
                <if test="search.enrollSchoolId != null and search.enrollSchoolId != ''
                    and search.adjustSchoolId != null and search.adjustSchoolId != '' ">
                    AND sb.enroll_school_id = #{search.enrollSchoolId}
                    AND sb.adjust_school_id = #{search.adjustSchoolId}
                </if>
            </otherwise>
        </choose>
    </select>


    <select id="getBatchAudit" resultType="com.shida.region.biz.pojo.vo.BatchAuditVo">
        SELECT
        sb.id,sb.education_review_status,sb.school_review_status,sb.publicity_status,sb.enroll_stage,
        sb.enroll_id,sb.user_id,sbi.student_id_card_number AS id_card
        FROM r_student_base AS sb
        LEFT JOIN r_student_base_info AS sbi ON sb.id = sbi.student_id
        LEFT JOIN t_registration_system_set_up_sava AS save ON sb.set_up_save_ids = save.setup_id
        LEFT JOIN `${dbName}`.t_dept AS td ON sb.enroll_school_id = td.id
        WHERE sb.delete_flag = 0  AND sb.adjustment = 0
        <!--报名阶段-->
        <if test="search.enrollStage != null and search.enrollStage != ''">
            AND sb.enroll_stage = #{search.enrollStage}
        </if>
        <!--姓名或者身份证-->
        <if test="search.studentName != null and search.studentName != ''">
            AND (sbi.student_name like CONCAT('%',#{search.studentName},'%') or
            sbi.student_id_card_number like CONCAT('%',#{search.studentName},'%'))
        </if>
        <!--是否公示(0未公示 1 已公示)-->
        <if test="search.publicityStatus != null and search.publicityStatus != ''">
            AND sb.publicity_status = #{search.publicityStatus}
        </if>
        <!--报名ID-->
        <if test="search.enrollId != null and search.enrollId != ''">
            AND sb.enroll_id like CONCAT('%',#{search.enrollId},'%')
        </if>
        <!--学校名称-->
        <if test="search.enrollSchoolName != null and search.enrollSchoolName != ''">
            AND sb.enroll_school_name like CONCAT('%',#{search.enrollSchoolName},'%')
        </if>
        <!--调剂学校名称-->
        <if test="search.adjustSchoolName != null and search.adjustSchoolName != ''">
            AND sb.adjust_school_name like CONCAT('%',#{search.adjustSchoolName},'%')
        </if>
        <!--学校性质-->
        <if test="search.nature != null and search.nature != ''">
            AND td.nature = #{search.nature}
        </if>
        <!--类别-->
        <if test="search.type != null and search.type != ''">
            AND save.ids_name like CONCAT('%',#{search.type},'%')
        </if>
        <!--报名开始时间,结束时间-->
        <if test="search.createdTime != null">
            AND sb.enroll_time &gt;= #{search.createdTime}
        </if>
        <if test="search.endTime != null">
            AND sb.enroll_time &lt;= #{search.endTime}
        </if>
        <!--学校审核状态-->
        <if test="search.schoolReviewStatus != null and search.schoolReviewStatus != ''">
            AND sb.school_review_status = #{search.schoolReviewStatus}
        </if>
        <!--教育局审核状态-->
        <if test="search.educationReviewStatus and search.educationReviewStatus != ''">
            AND sb.education_review_status = #{search.educationReviewStatus}
        </if>
        <!--公安审核状态-->
        <if test="search.publicSecurityReviewStatus != null and search.publicSecurityReviewStatus != ''">
            AND sb.public_security_review_status = #{search.publicSecurityReviewStatus}
        </if>
        <!--房产审核状态-->
        <if test="search.estateReviewStatus != null and search.estateReviewStatus != ''">
            AND sb.estate_review_status = #{search.estateReviewStatus}
        </if>
        <!--是否报道-->
        <if test="search.checkInStatus != null   ">
            AND sb.check_in_status = #{search.checkInStatus}
        </if>
        <!--是否发送录取通知书-->
        <if test="search.admissionLetterInStatus != null ">
            AND sb.admission_letter_in_status = #{search.admissionLetterInStatus}
        </if>
        <choose>
            <!--如果是学校账号
             OR (sb.adjust_school_id = #{search.enrollSchoolId})-->
            <when test="search.role != null and search.role == 'SCHOOL'">
                AND (
                (sb.enroll_school_id = #{search.enrollSchoolId} AND sb.adjust_school_id IS NULL )

                )
            </when>
            <otherwise>
                <!--学校ID-->
                <!--报名学校是A 调剂到B 那么 搜索条件是A 搜不到 搜索条件是B 搜不到，只有搜索条件是调剂学校才能搜到-->
                <if test="search.enrollSchoolId != null and search.enrollSchoolId != ''
                    and search.adjustSchoolId == null or search.adjustSchoolId == '' ">
                    AND sb.enroll_school_id = #{search.enrollSchoolId}
                    AND sb.adjust_school_id IS NULL
                </if>
                <!--调剂学校ID-->
                <if test="search.adjustSchoolId != null and search.adjustSchoolId != ''
                    and search.enrollSchoolId == null or search.enrollSchoolId == ''">
                    AND sb.adjust_school_id = #{search.adjustSchoolId}
                </if>
                <!--报名学校ID 调剂学校ID-->
                <if test="search.enrollSchoolId != null and search.enrollSchoolId != ''
                    and search.adjustSchoolId != null and search.adjustSchoolId != '' ">
                    AND sb.enroll_school_id = #{search.enrollSchoolId}
                    AND sb.adjust_school_id = #{search.adjustSchoolId}
                </if>
            </otherwise>
        </choose>

    </select>


    <select id="selectStudentAuditState" resultType="com.shida.region.biz.pojo.vo.BatchAuditVo">
        SELECT r1.id,
        r1.school_review_status,
        r1.education_review_status,
        r1.enroll_stage,
        r1.enroll_id,
        r1.user_id,
        r2.student_id_card_number AS id_card
        FROM r_student_base r1
        LEFT JOIN r_student_base_info r2
        ON r1.id = r2.student_id
        WHERE r1.id in
        <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        AND r1.adjustment = 0
    </select>


    <select id="selectStudentPublicStatus" resultType="java.lang.Integer">
        SELECT r_student_base.publicity_status
        FROM r_student_base
        LEFT JOIN r_student_base_info ON r_student_base.id = r_student_base_info.student_id
        WHERE r_student_base.id in
        <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        AND r_student_base.adjustment = 0
    </select>
    <select id="selectUserIdByIdCardNum" resultType="com.shida.region.biz.pojo.dto.StudentUserInfoDto">
        SELECT rsb.id, rsb.user_id, rsb.house_info_type,rsb.school_review_status,rsb.education_review_status,rsb.enroll_stage,rsb.set_up_save_ids,rsbi.student_id_card_number
        FROM r_student_base rsb
                 LEFT JOIN r_student_base_info rsbi
                           ON rsb.id = rsbi.student_id
        WHERE rsb.user_id = #{userId,jdbcType=BIGINT}
          AND rsb.delete_flag = 0
          AND rsbi.delete_flag = 0
    </select>

    <select id="getAddressFormatting" resultType="String">
        select range_name
        from `${dbName}`.t_school_rang
        where id = #{id}
    </select>
    <select id="selectSignNum" resultType="java.lang.Integer">
        SELECT
            ifnull(SUM( related_num + 1 ),0)
        FROM
            r_student_base
        WHERE
            delete_flag = 0
          AND enroll_stage = #{enrollStage}
          AND adjustment = 0
    </select>
    <select id="slectStudentCode" resultType="String">
        select student_name from `tshg-user-center`.t_student_code where id_card =#{studendCar}
    </select>
    <select id="isStudentZhiSheng"  parameterType="java.lang.Long" resultMap="studentZhiShengResultMap">
        SELECT
            si.student_name AS 学生姓名,
            si.student_id_card_number AS 身份证号,
            s.is_zhi_sheng AS 是否直升
        FROM
            r_student_base s
                JOIN
            r_student_base_info si ON s.id = si.student_id
        WHERE
            s.delete_flag = 0 AND si.delete_flag = 0
          AND s.id =  #{id}

        UNION ALL

        SELECT
            t.name AS 学生姓名,
            t.id_card_number AS 身份证号,
            t.is_zhi_sheng AS 是否直升
        FROM
            r_twins_info t
                JOIN
            r_student_base s ON t.student_id = s.id
                JOIN
            r_student_base_info si ON s.id = si.student_id
        WHERE
            t.delete_flag = 0 AND s.delete_flag = 0 AND si.delete_flag = 0
          AND t.student_id = #{id}
    </select>

    <!-- 结果映射 -->
    <resultMap id="studentZhiShengResultMap"  type="com.shida.region.biz.pojo.vo.StudentEnrollInfoVo">
        <result column="学生姓名" property="studentName" />
        <result column="身份证号" property="idCardNumber" />
        <result column="是否直升" property="isZhiSheng" />
    </resultMap>
</mapper>
