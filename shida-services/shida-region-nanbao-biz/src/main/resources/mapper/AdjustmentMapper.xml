<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shida.region.biz.mapper.AdjustmentMapper">


    <insert id="addAdjustmentStudent">
        INSERT INTO `${dbName}`.`t_dept_student` (`id`,
                                                  `sign_id`,
                                                  `dept_id`,
                                                  `dept_code`,
                                                  `is_five`,
                                                  `student_id`,
                                                  `student_name`,
                                                  `gender`,
                                                  `id_card`,
                                                  `student_code`,
                                                  `tiwns_num`,
                                                  `period`,
                                                  `from_dept_id`,
                                                  `from_school_id`,
                                                  `from_adjust_school_id`,
                                                  `to_dept_id`,
                                                  `to_school_id`,
                                                  `creator`,
                                                  `creator_name`,
                                                  `notice_status`,
                                                  `sign_status`,
                                                  `public_status`,
                                                  `url`,
                                                  `creator_role_code`,
                                                  `creator_role_name`,
                                                  `content`,
                                                  `create_time`,
                                                  `update_time`)
        VALUES (#{deptStudent.id},
                #{deptStudent.signId},
                #{deptStudent.deptId},
                #{deptStudent.deptCode},
                #{deptStudent.isFive},
                #{deptStudent.studentId},
                #{deptStudent.studentName},
                #{deptStudent.gender},
                #{deptStudent.idCard},
                #{deptStudent.studentCode},
                #{deptStudent.tiwnsNum},
                #{deptStudent.period},
                #{deptStudent.fromDeptId},
                #{deptStudent.fromSchoolId},
                #{deptStudent.fromAdjustSchoolId},
                #{deptStudent.toDeptId},
                #{deptStudent.toSchoolId},
                #{deptStudent.creator},
                #{deptStudent.creatorName},
                #{deptStudent.noticeStatus},
                #{deptStudent.signStatus},
                #{deptStudent.publicStatus},
                #{deptStudent.url},
                #{deptStudent.creatorRoleCode},
                #{deptStudent.creatorRoleName},
                #{deptStudent.content},
                #{deptStudent.createTime},
                #{deptStudent.updateTime});
    </insert>

    <insert id="addAdjustmentStudentLog">
        INSERT INTO `${dbName}`.`t_dept_student_log` (`id`,
                                                      `type`,
                                                      `dept_id`,
                                                      `student_id`,
                                                      `from_dept_id`,
                                                      `from_school_id`,
                                                      `to_dept_id`,
                                                      `to_school_id`,
                                                      `creator`,
                                                      `creator_name`,
                                                      `creator_role_code`,
                                                      `creator_role_name`,
                                                      `content`,
                                                      `create_time`,
                                                      `update_time`)
        VALUES (#{deptStudentLog.id},
                #{deptStudentLog.type},
                #{deptStudentLog.deptId},
                #{deptStudentLog.studentId},
                #{deptStudentLog.fromDeptId},
                #{deptStudentLog.fromSchoolId},
                #{deptStudentLog.toDeptId},
                #{deptStudentLog.toSchoolId},
                #{deptStudentLog.creator},
                #{deptStudentLog.creatorName},
                #{deptStudentLog.creatorRoleCode},
                #{deptStudentLog.creatorRoleName},
                #{deptStudentLog.content},
                #{deptStudentLog.createTime},
                #{deptStudentLog.updateTime});
    </insert>


    <insert id="addTwinsInfo">
        INSERT INTO `${dbName}`.`t_dept_twins` (`id`,
                                                `student_id`,
                                                `dept_id`,
                                                `student_name`,
                                                `gender`,
                                                `id_card`,
                                                `student_code`,
                                                `create_time`,
                                                `update_time`)
        VALUES (#{deptTwins.id},
                #{deptTwins.studentId},
                #{deptTwins.deptId},
                #{deptTwins.studentName},
                #{deptTwins.gender},
                #{deptTwins.idCard},
                #{deptTwins.studentCode},
                #{deptTwins.createTime},
                #{deptTwins.updateTime});
    </insert>


    <select id="getAdjustmentEnrollQuery" resultType="com.shida.region.biz.pojo.adjustment.DeptStudent">
        SELECT tds.`id`,
               tds.`sign_id`,
               tds.`dept_id`,
               tds.`dept_code`,
               tds.`is_five`,
               tds.`student_id`,
               tds.`student_name`,
               tds.`gender`,
               tds.`id_card`,
               tds.`student_code`,
               tds.`tiwns_num`,
               tds.`period`,
               tds.`from_dept_id`,
               tds.`from_school_id`,
               tds.`from_adjust_school_id`,
               tds.`to_dept_id`,
               tds.`to_school_id`,
               tds.`creator`,
               tds.`creator_name`,
               tds.`notice_status`,
               tds.`sign_status`,
               tds.`public_status`,
               tds.`url`,
               tds.`creator_role_code`,
               tds.`creator_role_name`,
               tds.`content`,
               tds.`create_time`,
               tds.`update_time`,
               td.dept_name AS toSchoolName
        FROM `${dbName}`.t_dept_student AS tds
                 LEFT JOIN `${dbName}`.t_dept AS td ON tds.to_school_id = td.id
        WHERE tds.student_id = #{studentId} AND tds.dept_id = #{deptId}
    </select>

    <select id="getDeptStudentContent" resultType="com.shida.region.biz.pojo.adjustment.DeptStudentContent">
        SELECT
            dept_id,
            student_id,
            sign_id,
            student_name,
            content,
            school_id,
            school_name
        FROM
            `${dbName}`.t_dept_student_content
        WHERE
            dept_id = #{deptId}
          AND student_id = #{studentId}
    </select>


</mapper>
