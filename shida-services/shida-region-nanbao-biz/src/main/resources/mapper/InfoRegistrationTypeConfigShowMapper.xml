<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shida.region.biz.mapper.InfoRegistrationTypeConfigShowMapper">
    <select id="selectMiddleConfigShow"
            resultType="com.shida.region.biz.pojo.dto.InfoRegistrationTypeConfigShowDto">
        SELECT irs.type_config_id,irs.set_up_save_ids,irs.is_show,itc.info_name
        FROM info_registration_type_config_show irs
        LEFT JOIN  info_type_config itc ON irs.type_config_id = itc.id
        WHERE itc.config_id = #{configId,jdbcType=BIGINT}
        AND irs.set_up_save_ids = #{setUpSaveIds,jdbcType=BIGINT}
    </select>
    <select id="selectShowTypeConfigId" resultType="com.shida.region.biz.pojo.dto.EnrollTypeConfigInfo">
        SELECT tcs.type_config_id,ts.info_name
        FROM info_registration_type_config_show tcs
        INNER JOIN (
            SELECT tc.id,tc.info_name
            FROM info_type_config tc
            LEFT JOIN info_registration_config_show cs
                ON cs.config_id = tc.config_id
            WHERE cs.set_up_save_ids = #{key,jdbcType=BIGINT}
                AND cs.is_show = 1
        ) ts
            ON tcs.type_config_id = ts.id
        WHERE tcs.is_show = 1
            AND tcs.set_up_save_ids = #{key,jdbcType=BIGINT}
    </select>
</mapper>
