package com.shida.region.biz.controller;

import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.annotation.SaMode;
import cn.hutool.core.util.StrUtil;
import com.shida.autolog.AutoLog;
import com.shida.autolog.LogType;
import com.shida.autolog.OperateType;
import com.shida.constant.RoleConstant;
import com.shida.enums.RestCode;
import com.shida.exceptions.ServiceException;
import com.shida.noRepeatSubmit.NoRepeatSubmit;
import com.shida.pojo.form.KeyForm;
import com.shida.pojo.search.Search;
import com.shida.pojo.vo.PageInfo;
import com.shida.region.biz.pojo.excel.ExcelBatchImportResponse;
import com.shida.region.biz.pojo.from.*;
import com.shida.region.biz.pojo.search.RecruitStudentSearch;
import com.shida.region.biz.pojo.vo.*;
import com.shida.region.biz.service.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static com.shida.constant.RoleConstant.*;

/**
 * Description:
 *
 * @Author: laobaduo
 * @Create: 2024/5/4 - 10:10
 */
@RestController
@RequestMapping("/biz/enrollment/")
@Tag(name = "学生报名")
public class EnrollmentController {

    @Resource
    private IInfoSetFieldService infoSetFieldService;
    @Resource
    private IStudentBaseService studentBaseService;
    @Resource
    private IStudentBaseInfoService studentBaseInfoService;
    @Resource
    private IWorkInfoService workInfoService;
    @Resource
    private IEstateInfoService estateInfoService;


    /**
     * 查询报名路径，第一次返回 乡镇报名，城区报名，第二次返回 公办小学，公办初中
     * @param form
     * @return
     */
    @PostMapping("getSetupSaveIds")
    @Operation(summary = "查询报名路径")
    public List<registrationEntranceVo> getSetupSaveIds(@RequestBody @Valid KeyForm<Long> form){
        return infoSetFieldService.getSetupSaveIds(form.getKey());
    }

    @PostMapping("getSetupSaveDetail")
    @Operation(summary = "查询报名路径详情")
    public List<signUpSortDetailVo> getSetupSaveDetail(@RequestBody @Valid KeyForm<Long> form){
        return infoSetFieldService.getSetupSaveDetail(form.getKey());
    }


    @PostMapping("getEnrollFieldConfig")
    @Operation(summary = "获取报名字段")
    public List<EnrollMiddleFieldVo> getEnrollFieldConfig(@RequestBody @Valid KeyForm<Long> form){
        return infoSetFieldService.getEnrollFieldConfig(form);
    }

    @PostMapping("submitEnrollInfo")
    @SaCheckRole(RoleConstant.PARENT_CODE)
    @Operation(summary = "提交报名信息")
    @NoRepeatSubmit(value = 10)
    @AutoLog(logType = LogType.OPERATE,operateType = OperateType.OPERATE_ADD)
    public String submitEnrollInfo(@RequestBody @Valid EnrollForm form,@RequestHeader("client-id") String clientId){
        if(StrUtil.isEmpty(clientId)){
            throw new ServiceException(RestCode.DATA_ERROR,"报名端错误");
        }
        form.setClientId(clientId);
        return studentBaseService.submitEnrollInfo(form);
    }

    @PostMapping("getEnrollEntrance")
    @Operation(summary = "修改报名信息-获取报名路径")
    public List<SetupSaveVo> getEnrollEntrance(@RequestBody KeyForm<Long> form ){
        return workInfoService.getEnrollEntrance(form.getKey());
    }

    @PostMapping("getSchoolEnrollEntrance")
    @Operation(summary = "修改报名信息-获取报名路径")
    public List<SetupSaveVo> getSchoolEnrollEntrance(@RequestBody KeyForm<Long> form ){
        return workInfoService.getSchoolEnrollEntrance(form.getKey());
    }


    @PostMapping("modifyGetEnrollFieldConfig")
    @SaCheckRole(value = {RoleConstant.SUPER_ADMIN_CODE,RoleConstant.COUNTY_ADMIN_CODE,RoleConstant.SCHOOL_CODE,RoleConstant.PARENT_CODE,RoleConstant.AUDITOR_CODE},mode = SaMode.OR)
    @Operation(summary = "修改报名信息-获取报名信息")
    public ModifyEnrollInfoVo modifyGetEnrollFieldConfig(@RequestBody @Valid ModifyGetFieldConfigForm form){
        return studentBaseService.modifyGetEnrollFieldConfig(form);
    }

    @PostMapping("rejectOrModifyEnrollInfo")
    @Operation(summary = "驳回或修改后提交报名信息")
    @SaCheckRole(RoleConstant.PARENT_CODE)
    @NoRepeatSubmit(value = 10)
    @AutoLog(logType = LogType.OPERATE,operateType = OperateType.OPERATE_EDIT)
    public String rejectOrModifyEnrollInfo(@RequestBody EnrollForm form,@RequestHeader("client-id") String clientId){
        if(StrUtil.isEmpty(clientId)){
            throw new ServiceException(RestCode.DATA_ERROR,"报名端错误");
        }
        form.setClientId(clientId);
        return studentBaseService.rejectOrModifyEnrollInfo(form);
    }


    @PostMapping("organizationSubmitEnrollInfo")
    @SaCheckRole(value = {RoleConstant.SCHOOL_CODE,RoleConstant.COUNTY_ADMIN_CODE},mode = SaMode.OR)
    @Operation(summary = "学校教育局提交报名信息")
    @NoRepeatSubmit(value = 10)
    public String organizationSubmitEnrollInfo(@RequestBody @Valid EnrollOrgForm form){
        return studentBaseService.organizationSubmitEnrollInfo(form);
    }

    @PostMapping("getEnrollStudentInfo")
    @SaCheckRole(value = {RoleConstant.SUPER_ADMIN_CODE,RoleConstant.CITY_ADMIN_CODE,RoleConstant.COUNTY_ADMIN_CODE}, mode = SaMode.OR)
    @Operation(summary = "查询清除学生数据")
    public ClearEnrollStudentInfoVo getEnrollStudentInfo(@RequestBody KeyForm<String> idCardNumber){
        return estateInfoService.getEnrollStudentInfo(idCardNumber.getKey());
    }

    @PostMapping("getClearStudentList")
    @SaCheckRole(value = {RoleConstant.SUPER_ADMIN_CODE,RoleConstant.CITY_ADMIN_CODE,RoleConstant.COUNTY_ADMIN_CODE}, mode = SaMode.OR)
    @Operation(summary = "查询清除学生列表")
    public PageInfo<ClearEnrollStudentInfoVo> getClearStudentList(@RequestBody Search idCardNumber){
        return estateInfoService.getClearStudentList(idCardNumber);
    }

    @PostMapping("deleteEnrollInfo")
    @SaCheckRole(value = {RoleConstant.SUPER_ADMIN_CODE,RoleConstant.CITY_ADMIN_CODE,RoleConstant.COUNTY_ADMIN_CODE}, mode = SaMode.OR)
    @Operation(summary = "清除学生报名")
    @AutoLog(logType = LogType.OPERATE,operateType = OperateType.OPERATE_DELETE)
    public Boolean deleteEnrollInfo(@RequestBody DeleteStudentForm form){
        return estateInfoService.clearEnrollInfo(form);
    }

    @PostMapping("exportBatchClearStuExcel")
    @SaCheckRole(value = {RoleConstant.SUPER_ADMIN_CODE,RoleConstant.CITY_ADMIN_CODE,RoleConstant.COUNTY_ADMIN_CODE}, mode = SaMode.OR)
    @Operation(summary = "导出批量学生报名数据模板")
    public void exportBatchClearStuExcel(HttpServletResponse response){
        estateInfoService.exportBatchClearStuExcel(response);
    }

    @PostMapping("importBatchClearStuExcel")
    @SaCheckRole(value = {RoleConstant.SUPER_ADMIN_CODE,RoleConstant.CITY_ADMIN_CODE,RoleConstant.COUNTY_ADMIN_CODE}, mode = SaMode.OR)
    @Operation(summary = "导入批量学生报名数据模板")
    @AutoLog(logType = LogType.OPERATE,operateType = OperateType.OPERATE_DELETE)
    public List<ExcelBatchImportResponse> importBatchClearStuExcel(@RequestParam("file") MultipartFile file) throws IOException {
        return estateInfoService.importBatchClearStuExcel(file);
    }

    @PostMapping("recoverEnrollInfo")
    @SaCheckRole(value = {RoleConstant.SUPER_ADMIN_CODE,RoleConstant.CITY_ADMIN_CODE,RoleConstant.COUNTY_ADMIN_CODE}, mode = SaMode.OR)
    @Operation(summary = "恢复清除的学生报名信息")
    @NoRepeatSubmit(value = 10)
    @AutoLog(logType = LogType.OPERATE,operateType = OperateType.OPERATE_EDIT)
    public Boolean recoverEnrollInfo(@RequestBody @Valid RecoverClearEnrollStuInfoForm form){
        return estateInfoService.recoverEnrollInfo(form);
    }

    @PostMapping("getStuEnrollInfo")
    @Operation(summary = "获取学生报名详情")
    @SaCheckRole(value = {RoleConstant.SUPER_ADMIN_CODE,RoleConstant.CITY_ADMIN_CODE,RoleConstant.COUNTY_ADMIN_CODE,RoleConstant.SCHOOL_CODE,RoleConstant.PARENT_CODE,
            RoleConstant.AUDITOR_CODE,RoleConstant.ZHU_JIAN,RoleConstant.SHEN_PI,RoleConstant.POLICE_CODE, RoleConstant.HOUSE_CODE,
            RoleConstant.ZI_GUI,RoleConstant.DIAN, RoleConstant.SHUI, RoleConstant.SI_FA,RoleConstant.CITY_ADMIN_CODE},mode = SaMode.OR)
    public StuEnrollInfoVo getStuEnrollInfo(@RequestBody @Valid KeyForm<Long> form){
        return studentBaseService.getStuEnrollInfo(form.getKey());
    }

    @PostMapping("getDynamicStuExportInfo")
    @Operation(summary = "动态导出学生信息")
    @AutoLog(operateType = OperateType.OPERATE_EXPORT)
    @SaCheckRole(value = {RoleConstant.SUPER_ADMIN_CODE,COUNTY_ADMIN_CODE,AUDITOR_CODE,SCHOOL_CODE},mode = SaMode.OR)
    public void getDynamicStuExportInfo(@RequestBody @Valid RecruitStudentSearch search
            , HttpServletResponse response)throws IOException {
        studentBaseInfoService.getDynamicStuExportInfo(search,response);
    }

    @PostMapping("getResidueModifyCount")
    @Operation(summary = "查询本区县剩余修改次数")
    public Integer getResidueModifyCount(@RequestBody @Valid KeyForm<String> form){
        return workInfoService.getResidueModifyCount(form.getKey());
    }

    @PostMapping("parentClearEnrollInfo")
    @SaCheckRole(value = {RoleConstant.PARENT_CODE,RoleConstant.SUPER_ADMIN_CODE},mode = SaMode.OR)
    @AutoLog(logType = LogType.OPERATE,operateType = OperateType.OPERATE_DELETE)
    @Operation(summary = "家长重新报名删除报名信息")
    @NoRepeatSubmit(value = 10)
    public Boolean parentClearEnrollInfo(@RequestBody @Valid KeyForm<Long> form){
        return workInfoService.parentClearEnrollInfo(form.getKey());
    }

    @PostMapping("getNextPath")
    @SaCheckRole(value = {RoleConstant.PARENT_CODE,RoleConstant.SUPER_ADMIN_CODE},mode = SaMode.OR)
    @AutoLog(logType = LogType.OPERATE,operateType = OperateType.OPERATE_DELETE)
    @Operation(summary = "获取按钮跳转页面")
    public Integer getReviewStatus(){
        return workInfoService.getReviewStatus();
    }

    @PostMapping("getEnrollRoute")
    @SaCheckRole(value = {RoleConstant.SCHOOL_CODE,RoleConstant.SUPER_ADMIN_CODE},mode = SaMode.OR)
    @Operation(summary = "学校获取报名路径")
    public List<SchoolEnrollSetupVo> getEnrollRoute(@RequestBody @Valid KeyForm<Long> form){
        return workInfoService.getEnrollRoute(form.getKey());
    }
    @PostMapping("fnanRedis")
    @Operation(summary = "获取学生报名详情")
    @SaCheckRole(value = {RoleConstant.SUPER_ADMIN_CODE,RoleConstant.CITY_ADMIN_CODE,RoleConstant.COUNTY_ADMIN_CODE,RoleConstant.SCHOOL_CODE,RoleConstant.PARENT_CODE,
            RoleConstant.AUDITOR_CODE,RoleConstant.ZHU_JIAN,RoleConstant.SHEN_PI,RoleConstant.POLICE_CODE, RoleConstant.HOUSE_CODE,
            RoleConstant.ZI_GUI,RoleConstant.DIAN, RoleConstant.SHUI, RoleConstant.SI_FA,RoleConstant.CITY_ADMIN_CODE},mode = SaMode.OR)
    public     List<String>  fnanRedis(){
        return studentBaseService.fnanRedis();
    }
}
