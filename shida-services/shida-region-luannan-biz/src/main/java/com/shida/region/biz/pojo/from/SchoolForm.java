package com.shida.region.biz.pojo.from;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

@Data
@Accessors(chain = true)
@Schema(name = "SchoolForm", description = "学校信息")
public class SchoolForm implements Serializable {
    @Schema(description = "id")
    private Long id;

    @Schema(description = "区县id")
    private Long parentId;

    @Schema(description = "学校名称")
    @Size(min = 1, max = 60, message = "学校名称长度为8-64位")
    @NotBlank(message = "学校名称不能为空")
    private String deptName;

    @Schema(description = "level")
    private Integer level = 3;

    @Schema(description = "学校地址")
    @Size(min = 1, max = 200, message = "学校地址长度为1-200位")
    @NotBlank(message = "学校地址不能为空")
    private String address;

    @Schema(description = "管理员")
    private String master;

    @Schema(description = "手机号码")
    private String mobile;

    @Schema(description = "学段（1幼儿园；2小学；3初中）")
    @NotBlank(message = "学段不能为空")
    private String period;

    @Schema(description = "办学类型（1公办2民办）")
    @NotBlank(message = "办学类型不能为空")
    private String type;

    @Schema(description = "学校性质（1乡镇2城区）")
    @NotBlank(message = "学校性质不能为空")
    private String nature ;

    @Schema(description = "招生计划数")
    private Integer planNum = 0;

    @Schema(description = "学校简介")
    private String content;

    @Schema(description = "招生范围")
    private String area;

    @Schema(description = "招生类别")
    @NotBlank(message = "招生类别不能为空")
    private String enrollType;
    @Schema(description = "学校标识")
    private String schoolBiaoShi;

}
