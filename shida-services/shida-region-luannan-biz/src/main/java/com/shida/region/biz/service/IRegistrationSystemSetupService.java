package com.shida.region.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.shida.region.biz.entity.RegistrationSystemSetup;
import com.shida.region.biz.pojo.from.SaveSystemSetUpTreeForm;
import com.shida.region.biz.pojo.from.SchoolForm;
import com.shida.region.biz.pojo.vo.RegistrationSystemSetupVo;
import com.shida.region.biz.pojo.vo.SchoolEnrollTypeVo;
import com.shida.region.biz.pojo.vo.registrationEntranceVo;

import java.util.List;
import java.util.Map;


public interface IRegistrationSystemSetupService extends IService<RegistrationSystemSetup> {

    /**
     * 根据提供的设置保存ID获取注册类型。
     * 该方法首先会根据提供的ID获取一个注册系统的设置，然后基于这个ID的父ID三次，组合最后得到的名称和初始名称作为注册类型返回。
     *
     * @param setUpSaveIds 设置保存的ID，用于查询注册类型。
     * @return 返回组合后的注册类型字符串，格式为“父级名称-子级名称”。
     */
    registrationEntranceVo getRegistrationType(Long setUpSaveIds);
    /**
     * 系统设置 树状图
     * @return
     */
    public List<RegistrationSystemSetupVo> getSystemSetUpTree();

    /**
     * 保存 信息
     * @param form
     * @return
     */
    public Boolean saveSystemSetUp(SaveSystemSetUpTreeForm form);


    /**
     * 功能大纲
     * @return
     */
    public List<RegistrationSystemSetupVo> functionalOutlineTree();


    /**
     * 回显树
     * @param
     * @return
     */
    public List<Long> echoSystemSetUpTree();

    /**
     * 报名设置列表回显
     * @return
     */
    public  Map<String,Object> echoSignUpSetUpList();

    /**
     * 查询学校报名类型
     * 根据学校的性质（乡镇/城区）、学段、办学类型查询对应的报名类型
     * 并查询当前学校已存在的报名类型进行回显
     * @param form 学校信息表单
     * @return 查询结果
     */
    SchoolEnrollTypeVo selectSchoolSetUpIds(SchoolForm form);

}
