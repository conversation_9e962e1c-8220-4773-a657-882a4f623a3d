package com.shida.region.biz.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shida.entity.BaseEntity;
import com.shida.enums.RestCode;
import com.shida.exceptions.ServiceException;
import com.shida.region.biz.entity.*;
import com.shida.region.biz.mapper.*;
import com.shida.region.biz.pojo.from.SaveSystemSetUpTreeForm;
import com.shida.region.biz.pojo.from.SchoolForm;
import com.shida.region.biz.pojo.vo.RegistrationSystemSetupVo;
import com.shida.region.biz.pojo.vo.SchoolEnrollTypeVo;
import com.shida.region.biz.pojo.vo.registrationEntranceVo;
import com.shida.region.biz.service.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


@Service
public class RegistrationSystemSetupServiceImpl extends ServiceImpl<RegistrationSystemSetupMapper, RegistrationSystemSetup> implements IRegistrationSystemSetupService {

    @Resource
    private IRegistrationSystemSetUpSavaService registrationSystemSetUpSavaService;

    @Resource
    private IInfoRegistrationConfigShowService infoRegistrationConfigShowService;

    @Resource
    private IInfoRegistrationTypeConfigShowService infoRegistrationTypeConfigShowService;

    @Resource
    private RegistrationSystemSetupMapper registrationSystemSetupMapper ;
    @Resource
    private IInfoConfigService iInfoConfigService;

    @Resource
    private IInfoTypeConfigService iInfoTypeConfigService;


    @Override
    public registrationEntranceVo getRegistrationType(Long setUpSaveIds) {
        String registrationType = "";
        // 根据提供的ID获取设置名称
        RegistrationSystemSetup idsNameBySaveIds = getIdsNameBySaveIds(setUpSaveIds);
        registrationType = idsNameBySaveIds.getName();

        // 递归获取父级设置名称，重复三次
        idsNameBySaveIds = getIdsNameBySaveIds(idsNameBySaveIds.getParentId());
        idsNameBySaveIds = getIdsNameBySaveIds(idsNameBySaveIds.getParentId());
        idsNameBySaveIds = getIdsNameBySaveIds(idsNameBySaveIds.getParentId());
        // 组合父级名称和初始名称作为最终的注册类型
        registrationType = idsNameBySaveIds.getName() + "-" + registrationType;
       return new registrationEntranceVo().setId(idsNameBySaveIds.getId()).setName(registrationType);
    }


    /**
     * 根据保存的ID获取注册系统设置的名称。
     *
     * @param setUpSaveIds 保存的设置ID，类型为Long。
     * @return RegistrationSystemSetup 返回查询到的注册系统设置对象。
     * @throws ServiceException 如果查询不到相应的设置信息，则抛出运行时异常。
     */
    private RegistrationSystemSetup getIdsNameBySaveIds(Long setUpSaveIds) {
        // 使用LambdaQueryWrapper查询指定ID的注册系统设置
        RegistrationSystemSetup registrationSystemSetup = baseMapper.selectOne(new LambdaQueryWrapper<RegistrationSystemSetup>().eq(RegistrationSystemSetup::getId, setUpSaveIds));

        // 如果查询结果为空，则抛出运行时异常
        if (Objects.isNull(registrationSystemSetup)) {
            throw new ServiceException(RestCode.DATA_ERROR,"系统设置 树状图 异常");
        }

        return registrationSystemSetup;
    }

    /**
     * 系统设置 树状图
     *
     * @return
     */
    @Override
    public List<RegistrationSystemSetupVo> getSystemSetUpTree() {
        LambdaQueryWrapper<RegistrationSystemSetup> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        //设置查询条件 父级ID 不等于1
        lambdaQueryWrapper.eq(RegistrationSystemSetup::getParentId, -1L);
        List<RegistrationSystemSetup> registrationSystemSetups = baseMapper.selectList(lambdaQueryWrapper);

        if (ObjectUtil.isEmpty(registrationSystemSetups)) {
            throw new ServiceException(RestCode.DATA_ERROR,"数据库初始化异常,请联系管理员");
        }

        //返回前端 vo
        List<RegistrationSystemSetupVo> registrationSystemSetupVoList = new ArrayList<>();

        //创建LIST 存放数据
        List<RegistrationSystemSetupVo> registrationSystemSetupVoList1 = new ArrayList<>();

        //将数据 copy 到 vo 中
        for (int i = 0; i < registrationSystemSetups.size(); i++) {
            RegistrationSystemSetupVo registrationSystemSetupVo1 = new RegistrationSystemSetupVo();
            BeanUtil.copyProperties(registrationSystemSetups.get(i), registrationSystemSetupVo1);
            registrationSystemSetupVoList.add(registrationSystemSetupVo1);
        }

        for (RegistrationSystemSetupVo systemSetupVo : registrationSystemSetupVoList) {
            //ID 不为空 则递归
            if (systemSetupVo.getId() != null) {
                RegistrationSystemSetupVo registrationSystemSetups1 = getRegistrationSystemSetup(systemSetupVo);
                //数据不为空 将数据存放到 list
                if (registrationSystemSetups1 != null) {
                    registrationSystemSetupVoList1.add(registrationSystemSetups1);
                }

            }

        }
        return registrationSystemSetupVoList1;
    }


    /**
     * 保存 信息
     *
     * @param form
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveSystemSetUp(SaveSystemSetUpTreeForm form) {
        //交集
        List<Long> intersection = new ArrayList<>();
        //上传上来的差集
        List<Long> uploadDiff = new ArrayList<>();
        //查询出来的差集
        List<Long> selectDiff = new ArrayList<>();
        List<RegistrationSystemSetUpSava> setUpSavaList = registrationSystemSetUpSavaService.list();
        List<RegistrationSystemSetup> systemSetup = this.list();
        List<Long> setUpIdList = setUpSavaList.stream().map(RegistrationSystemSetUpSava::getSetupId).collect(Collectors.toList());
        if(CollUtil.isEmpty(setUpIdList)){
            uploadDiff = form.getChildren();
        }else{
            intersection = form.getChildren().stream().filter(setUpIdList::contains).collect(Collectors.toList());
            form.getChildren().removeAll(intersection);
            setUpIdList.removeAll(intersection);
            uploadDiff = form.getChildren();
            selectDiff = setUpIdList;
        }
        //交集不动，上传上来的差集先查询config_show有没有，有的则只更新set_up_sava表，没有的则更新set_up_sava,config_show,type_config_show表
        if(CollUtil.isNotEmpty(uploadDiff)){
            //更新set_up_sava表
            List<RegistrationSystemSetUpSava> setUpSavas = new ArrayList<>();
            uploadDiff.forEach(u -> {
                RegistrationSystemSetUpSava setUpSava = new RegistrationSystemSetUpSava();
                RegistrationSystemSetup currentSetUp = systemSetup.stream().filter(s -> ObjectUtil.equal(s.getId(), u)).collect(Collectors.toList()).get(0);
                setUpSava.setSetupId(u)
                        .setIdsName(currentSetUp.getName())
                        .setPath(currentSetUp.getParentId());
                setUpSavas.add(setUpSava);
            });
            registrationSystemSetUpSavaService.saveBatch(setUpSavas);
            //没有的更新set_up_sava,config_show,type_config_show表
            List<InfoRegistrationConfigShow> configShowExistList = infoRegistrationConfigShowService.getBaseMapper().selectList(new LambdaQueryWrapper<InfoRegistrationConfigShow>()
                    .in(InfoRegistrationConfigShow::getSetUpSaveIds, uploadDiff));
            if(CollUtil.isNotEmpty(configShowExistList)){
                List<Long> collect = configShowExistList.stream().map(InfoRegistrationConfigShow::getSetUpSaveIds).collect(Collectors.toList());
                uploadDiff.removeAll(collect);
            }
            if(CollUtil.isNotEmpty(uploadDiff)){
                //上传的交集，config_show,type_config_show表有没有值的
                List<InfoRegistrationConfigShow> configShows = new ArrayList<>();
                List<InfoRegistrationTypeConfigShow> typeConfigShows = new ArrayList<>();
                uploadDiff.forEach(u -> {
                    for(int i = 1; i <= 3; i++ ){
                        InfoRegistrationConfigShow in = new InfoRegistrationConfigShow();
                        in.setIsShow(1);//是否显示
                        in.setConfigId((long) i);
                        in.setSetUpSaveIds(u);
                        configShows.add(in);
                    }
                    for (int i = 1; i <= 18; i++) {
                        InfoRegistrationTypeConfigShow inType = new InfoRegistrationTypeConfigShow();
                        inType.setIsShow(0);//是否显示
                        inType.setTypeConfigId((long) i);
                        inType.setSetUpSaveIds(u);
                        typeConfigShows.add(inType);
                    }
                });
                infoRegistrationConfigShowService.saveBatch(configShows);
                infoRegistrationTypeConfigShowService.saveBatch(typeConfigShows);
            }
        }
        //交集不动，查询出来的差集删除set_up_sava信息
        if(CollUtil.isNotEmpty(selectDiff)){
            registrationSystemSetUpSavaService.getBaseMapper().delete(new LambdaQueryWrapper<RegistrationSystemSetUpSava>()
                    .in(RegistrationSystemSetUpSava::getSetupId, selectDiff));
        }
        return true;
    }


    /**
     * 功能大纲
     *
     * @return
     */
    @Override
    public List<RegistrationSystemSetupVo> functionalOutlineTree() {

        //查询出全部数据
        LambdaQueryWrapper<RegistrationSystemSetup> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(RegistrationSystemSetup::getParentId, 1L);

        List<RegistrationSystemSetup> all = baseMapper.selectList(lambdaQueryWrapper);

        //返回前端 vo
        List<RegistrationSystemSetupVo> registrationSystemSetupVoList = new ArrayList<>();

        //创建LIST 存放数据
        List<RegistrationSystemSetupVo> registrationSystemSetupVoList1 = new ArrayList<>();

        //将数据 copy 到 vo 中
        for (int i = 0; i < all.size(); i++) {
            RegistrationSystemSetupVo registrationSystemSetupVo1 = new RegistrationSystemSetupVo();
            BeanUtil.copyProperties(all.get(i), registrationSystemSetupVo1);
            registrationSystemSetupVoList.add(registrationSystemSetupVo1);
        }

        for (RegistrationSystemSetupVo registrationSystemSetupVo : registrationSystemSetupVoList) {
            //ID 不为空 则递归
            if (registrationSystemSetupVo.getId() != null) {
                RegistrationSystemSetupVo registrationSystemSetups1 = getRegistrationSystemSetup(registrationSystemSetupVo);
                //数据不为空 将数据存放到 list
                if (registrationSystemSetups1 != null) {
                    registrationSystemSetupVoList1.add(registrationSystemSetups1);
                }

            }
        }

        //查询 保存的全部子集ID
//        LambdaQueryWrapper<RegistrationSystemSetUpSava> lambdaQueryWrapper1 = new LambdaQueryWrapper<>();
//        lambdaQueryWrapper1.eq(RegistrationSystemSetUpSava::getTStuId, id);
//
//        List<Long> longs = registrationSystemSetUpSavaService.list(lambdaQueryWrapper1).stream().
//                map(registrationSystemSetUpSava -> registrationSystemSetUpSava.getSetupId()).collect(Collectors.toList());


        List<Long> longs = registrationSystemSetUpSavaService.list().stream().map(item -> item.getSetupId()).collect(Collectors.toList());

//        System.out.println(longs);

        //使用 set 保存，不需要去重
        Set<Long> pids = new HashSet<>();

        for (int i = 0; i < longs.size(); i++) {

            List<Long> b = processTree(longs.get(i), new ArrayList<>());
            pids.addAll(b);
        }
        pids.addAll(longs);
        //stream 去重
//        pids = pids.stream().distinct().collect(Collectors.toList());

        List<RegistrationSystemSetupVo> bySaveTree = getBySaveTree(registrationSystemSetupVoList1, pids);

        return bySaveTree;
    }

    /**
     * 回显树
     *
     * @param
     * @return
     */
    @Override
    public List<Long> echoSystemSetUpTree() {

        List<Long> collect = registrationSystemSetUpSavaService.list().stream().map(registrationSystemSetUpSava -> registrationSystemSetUpSava.getSetupId()).collect(Collectors.toList());
        return collect;
    }

    /**
     * 报名设置列表回显
     *
     * @return
     */
    @Override
    public Map<String, Object> echoSignUpSetUpList() {
        //查询全部数据
        List<RegistrationSystemSetUpSava> registrationSystemSetUpSavaList = registrationSystemSetUpSavaService.list();

        //所有父级id
        List<Long> path = registrationSystemSetUpSavaList.stream().map(registrationSystemSetUpSava -> registrationSystemSetUpSava.getPath()).distinct().collect(Collectors.toList());

        //{id:'19942464',{'名字','名字','名字'}},
        // {id:'19942464',{'名字','名字','名字'}}
        //创建存储 字符串集合 k:id v:String
        Map<Long, StringBuffer> nameMap = new HashMap<>();
        Map<String, StringBuffer> map1 = new HashMap<>();
        List<StringBuffer> list1 = new ArrayList<>();
        for (int i = 0; i < path.size(); i++) {
            Long aLong = path.get(i);

            RegistrationSystemSetup registrationSystemSetup3 = baseMapper.selectById(aLong);
            RegistrationSystemSetup registrationSystemSetup2 = baseMapper.selectById(registrationSystemSetup3.getParentId());
            RegistrationSystemSetup registrationSystemSetup1 = baseMapper.selectById(registrationSystemSetup2.getParentId());

            //第一次拼接字符串
            StringBuffer buffer = new StringBuffer();
//            buffer.append(registrationSystemSetup1.getName()+"--");
//            buffer.append(registrationSystemSetup2.getName());
//            buffer.append(registrationSystemSetup3.getName()+":");

            buffer.append(registrationSystemSetup1.getName() + ",");
            buffer.append(registrationSystemSetup2.getName() + ",");
            buffer.append(registrationSystemSetup3.getName() + ",");

            nameMap.put(aLong, buffer);

            StringBuffer buffer1 = new StringBuffer();
            buffer1.append(registrationSystemSetup1.getName());

            map1.put("id", buffer1);
            list1.add(buffer1);

        }

//        List<Map<String, Object>> listAll = new ArrayList<>();

        Map<String, Object> map = new HashMap<>();
        //报名类别
        List<StringBuffer> categoryOfRegistration = new ArrayList<>();


        //创建 拼接字符串
        //入口
        StringBuffer entrance = new StringBuffer();
        //学段
        StringBuffer field = new StringBuffer();

        for (int i = 0; i < path.size(); i++) {
            //拿到父级ID 去做比对
            Long aLong = path.get(i);
            StringBuffer buffer = new StringBuffer();

            for (int l = 0; l < registrationSystemSetUpSavaList.size(); l++) {
                RegistrationSystemSetUpSava reSava = registrationSystemSetUpSavaList.get(l);
                //如果父ID 相同 则说明在同一个子集里
                if (aLong == reSava.getPath()) {
                    //拿到当前名称
                    StringBuffer name = nameMap.get(reSava.getPath());
                    String[] split = name.toString().split(",");
                    //如果长度小于0 则第一次拼接字符串，否则，拼接下一个
                    if (buffer.indexOf(name.toString()) < 0) {
                        buffer.append(name).append(reSava.getIdsName());

                    } else {
                        buffer.append(reSava.getIdsName());

                    }
                    buffer.append(",");


                    if (entrance.indexOf(split[0]) < 0) entrance.append(split[0] + ",");
                    if (field.indexOf(split[2]) < 0) field.append(split[2] + ",");
                }
            }
            if (StringUtils.isNotEmpty(buffer)) {

                buffer.deleteCharAt(buffer.length() - 1);
                buffer.replace(buffer.indexOf(","), buffer.indexOf(",") + 1, "--");
                buffer.deleteCharAt(buffer.indexOf(","));
                buffer.replace(buffer.indexOf(","), buffer.indexOf(",") + 1, ":");
                buffer.append(";");



//                    nameRu.deleteCharAt(nameRu.length() -1);
//                    nameXue.deleteCharAt(nameXue.length() -1);

//                buffer.append(";");
//                buffer.deleteCharAt(buffer.indexOf(","));
                categoryOfRegistration.add(buffer);


            }
        }
        //去逗号
        entrance.deleteCharAt(entrance.length() - 1);
        field.deleteCharAt(field.length() - 1);

        map.put("categoryOfRegistration", categoryOfRegistration);
        map.put("entrance", entrance);
        map.put("field", field);


//        listAll.add(map);

        return map;
    }


    //    List<Long> pids = new ArrayList<>();
    private List<Long> processTree(Long id, List<Long> pids) {
        LambdaQueryWrapper<RegistrationSystemSetup> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        //设置查询条件 id = id //设置查询条件 父级ID 不等于-1
        lambdaQueryWrapper.eq(RegistrationSystemSetup::getId, id)
                .ne(RegistrationSystemSetup::getParentId, -1L).last("LIMIT 1");

        //查询 出当前数据
        RegistrationSystemSetup registrationSystemSetups = baseMapper.selectOne(lambdaQueryWrapper);

        if (ObjectUtil.isNotEmpty(registrationSystemSetups)) {
            Long pid = registrationSystemSetups.getParentId();
            pids.add(pid);
            //递归查询 父级 id
            return processTree(pid, pids);
        }
        return pids;
    }


    private List<RegistrationSystemSetupVo> getBySaveTree(List<RegistrationSystemSetupVo> list, Set<Long> longs) {

        //迭代器删除
        Iterator<RegistrationSystemSetupVo> iterator = list.iterator();
        //下一个元素
        while (iterator.hasNext()) {
            //获取下一个 对象
            RegistrationSystemSetupVo next = iterator.next();
            //看 longs 里是否包含 id
            List<RegistrationSystemSetupVo> children = next.getChildren();
            if (!longs.contains(next.getId())) {
                iterator.remove();
            } else {
                getBySaveTree(children, longs);
            }
        }
        return list;
    }

    /**
     * 系统设置
     * 递归 方法
     *
     * @param registrationSystemSetupVo
     * @return
     */
    private RegistrationSystemSetupVo getRegistrationSystemSetup(RegistrationSystemSetupVo registrationSystemSetupVo) {

        //查询全部 数据
        LambdaQueryWrapper<RegistrationSystemSetup> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(RegistrationSystemSetup::getParentId, registrationSystemSetupVo.getId());

        List<RegistrationSystemSetup> registrationSystemSetupList = baseMapper.selectList(lambdaQueryWrapper);

        List<RegistrationSystemSetupVo> registrationSystemSetupVoList = new ArrayList<>();

        for (int i = 0; i < registrationSystemSetupList.size(); i++) {
            RegistrationSystemSetupVo registrationSystemSetupVo1 = new RegistrationSystemSetupVo();
            BeanUtil.copyProperties(registrationSystemSetupList.get(i), registrationSystemSetupVo1);
            registrationSystemSetupVoList.add(registrationSystemSetupVo1);
        }

        if (registrationSystemSetupList != null) {
            registrationSystemSetupVo.setChildren(registrationSystemSetupVoList);

            for (RegistrationSystemSetupVo systemSetupVo : registrationSystemSetupVoList) {
                getRegistrationSystemSetup(systemSetupVo);
            }

        }
        return registrationSystemSetupVo;
    }

    /**
     * 查询学校报名类型
     * 根据学校的性质（乡镇/城区）、学段、办学类型查询对应的报名类型
     * 并查询当前学校已存在的报名类型进行回显
     * @param form 学校信息表单
     * @return 查询结果
     */
    @Override
    public SchoolEnrollTypeVo selectSchoolSetUpIds(SchoolForm form) {
        SchoolEnrollTypeVo result = new SchoolEnrollTypeVo();

        // 1. 根据学校性质、学段、办学类型查询对应的报名类型
        List<String> availableEnrollTypes = registrationSystemSetupMapper.getAvailableEnrollTypes(form);
        result.setAvailableEnrollTypes(availableEnrollTypes);
        // 查询当前学校所有的报名类型 学校id进行查询
        String currentSchoolEnrollTypes = registrationSystemSetupMapper.getCurrentSchoolEnrollTypes(form);
        if (StrUtil.isNotBlank(currentSchoolEnrollTypes)) {
            result.setCurrentSchoolEnrollTypes(Arrays.asList(currentSchoolEnrollTypes.split(",")));
        }

        return result;
    }


}
