package com.shida.region.biz.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.shida.region.biz.entity.RegistrationSystemSetup;
import com.shida.region.biz.entity.InfoConfig;
import com.shida.region.biz.entity.InfoTypeConfig;
import com.shida.region.biz.pojo.from.SchoolForm;
import com.shida.region.biz.pojo.vo.SchoolEnrollTypeVo;
import io.lettuce.core.dynamic.annotation.Param;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.List;
import java.util.Map;

public interface RegistrationSystemSetupMapper extends BaseMapper<RegistrationSystemSetup> {


    RegistrationSystemSetup selectEnrollStage(Long setUpSaveIds);

    List<String> getAvailableEnrollTypes(@Param("form") SchoolForm form);

    String getCurrentSchoolEnrollTypes(@Param("form") SchoolForm form);
}
