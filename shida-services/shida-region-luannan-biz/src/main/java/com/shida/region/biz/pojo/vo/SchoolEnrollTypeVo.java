package com.shida.region.biz.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 学校报名类型查询结果VO
 */
@Data
@Accessors(chain = true)
@Schema(name = "SchoolEnrollTypeVo", description = "学校报名类型查询结果")
public class SchoolEnrollTypeVo implements Serializable {

    @Schema(description = "所有可用的报名类型列表")
    private List<String> availableEnrollTypes;

    @Schema(description = "当前学校已选择的报名类型")
    private List<String> currentSchoolEnrollTypes;


}
