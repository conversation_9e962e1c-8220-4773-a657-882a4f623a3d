<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shida.region.biz.mapper.RegistrationSystemSetupMapper">


    <select id="selectEnrollStage" resultType="com.shida.region.biz.entity.RegistrationSystemSetup">
        SELECT t1.id,t1.name
        FROM t_registration_system_setup t1
        LEFT JOIN t_registration_system_setup t2
            ON t1.id = t2.parent_id
        WHERE t2.id = #{setUpSaveIds,jdbcType=BIGINT}

    </select>
    <select id="getAvailableEnrollTypes" resultType="java.lang.String">
        SELECT
            final_types.name
        FROM
            t_registration_system_setup AS final_types
        WHERE
            final_types.parent_id = (
                SELECT
                    period_level.id
                FROM
                    t_registration_system_setup AS nature_level,
                    t_registration_system_setup AS type_level,
                    t_registration_system_setup AS period_level
                WHERE
                    type_level.parent_id = nature_level.id
                  AND period_level.parent_id = type_level.id

                  AND nature_level.name = (
                    CASE #{form.nature}
                        WHEN '1' THEN '乡镇报名'
                        WHEN '2' THEN '城区报名'
                        END
                    )
                  AND type_level.name = (
                    CASE #{form.type}
                        WHEN '1' THEN '公办'
                        WHEN '2' THEN '民办'
                        END
                    )
                  AND period_level.name = (
                    CASE #{form.period}
                        WHEN '1' THEN '幼儿园'
                        WHEN '2' THEN '小学'
                        WHEN '3' THEN '初中'
                        END
                    )
            )
    </select>
    <select id="getCurrentSchoolEnrollTypes" resultType="java.lang.String">
        select  `tshg-user-center`.t_dept.enroll_type from `tshg-user-center`.t_dept where dept_name = #{form.deptName}
    </select>
</mapper>
