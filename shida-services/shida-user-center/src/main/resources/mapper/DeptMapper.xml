<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shida.user.center.mapper.DeptMapper">


    <select id="selectDeptInfo" resultType="com.shida.user.center.pojo.vo.DeptInfo">
        select t1.*, t2.content from t_dept t1
        left join t_school_content t2 on t1.id = t2.school_id
        where t1.id = #{id}
    </select>

    <select id="getDeptList" resultType="com.shida.user.center.pojo.vo.StudentStatisticsInfo">
        SELECT
            id AS dept_id,
            dept_code,
            dept_name,
            is_five,
            privatee_status,
            0 AS privatePrimaryNum ,
            0 AS privatePrimaryAdmissionNum ,
            0 AS privateJuniorNum ,
            0 AS privateJuniorAdmissionNum ,
            0 AS publicPrimaryNum ,
            0 AS publicPrimaryAdmissionNum ,
            0 AS publicJuniorNum ,
            0 AS publicJuniorAdmissionNum
        FROM
            t_dept
        WHERE LEVEL = 2 AND dept_code IN (
                                          '130209',
                                          '130207',
                                          '130204',
                                          '130274',
                                          '130272',
                                          '130205',
                                          '130225',
                                          '130224',
                                          '130284',
                                          '130202',
                                          '130271',
                                          '130285'
            )
        ORDER BY
            sort,
            dept_code DESC
    </select>


    <select id="getOtherDeptStudentStatistics" resultType="com.shida.user.center.pojo.vo.StudentStatisticsInfo">
        select ${deptCode} as deptCode,
            sum(case when rsb.enroll_stage = 2 and rsb.school_type = '1' then (1 + IFNULL(rti.tws,0))  else 0 end) as publicPrimaryNum,
            sum(case when rsb.enroll_stage = 3 and rsb.school_type = '1' then (1 + IFNULL(rti.tws,0))  else 0 end) as publicJuniorNum,
            sum(case when rsb.enroll_stage = 2 and rsb.school_type = '1' AND rsb.education_review_status = 2 and rsb.publicity_status = 1 then (1 + IFNULL(rti.tws,0))  else 0 end) as publicPrimaryAdmissionNum,
            sum(case when rsb.enroll_stage = 3 and rsb.school_type = '1' AND rsb.education_review_status = 2 and rsb.publicity_status = 1  then (1 + IFNULL(rti.tws,0))  else 0 end) as publicJuniorAdmissionNum,
        <choose>
            <when test="deptCode == '130284'">
                sum(case when rsb.enroll_stage = 3   AND rsb.outside_School_Type IS NULL  and rsb.school_type = '2' then (1 + IFNULL(rti.tws,0))  else 0 end) as privateJuniorNum,
            </when>
            <otherwise>
                sum(case when rsb.enroll_stage = 3 and rsb.school_type = '2' then (1 + IFNULL(rti.tws,0))  else 0 end) as privateJuniorNum,
            </otherwise>
        </choose>
        sum(case when rsb.enroll_stage = 2 and rsb.school_type = '2' then (1 + IFNULL(rti.tws,0))  else 0 end) as privatePrimaryNum,

             sum(case when rsb.enroll_stage = 2 and rsb.school_type = '2' AND rsb.education_review_status = 2 and rsb.publicity_status = 1  then (1 + IFNULL(rti.tws,0))  else 0 end) as privatePrimaryAdmissionNum,
            sum(case when rsb.enroll_stage = 3 and rsb.school_type = '2' AND rsb.education_review_status = 2 and rsb.publicity_status = 1  then (1 + IFNULL(rti.tws,0))  else 0 end) as privateJuniorAdmissionNum
            from `tshg-region-biz-${deptCode}`.r_student_base rsb
            left join
            (select student_id, count(1) tws from `tshg-region-biz-${deptCode}`.r_twins_info  GROUP BY student_id) rti
            ON rti.student_id = rsb.id
            WHERE
            rsb.delete_flag = 0 and rsb.adjustment = 0
    </select>

    <select id="getPublicDeptStudentStatistics" resultType="com.shida.user.center.pojo.vo.StudentStatisticsInfo">
        select ${deptCode} as deptCode,
            sum(case when rsb.enroll_stage = 2  then (1 + IFNULL(rti.tws,0))  else 0 end) as publicPrimaryNum,
        <choose>
            <when test="deptCode == '130209'">
                sum(case when rsb.enroll_stage = 3  and rsb.is_transfer=0  then (1 + IFNULL(rti.tws,0))  else 0 end) as publicJuniorNum,  </when>
            <otherwise>
                sum(case when rsb.enroll_stage = 3  then (1 + IFNULL(rti.tws,0))  else 0 end) as publicJuniorNum,   </otherwise>
        </choose>
            sum(case when rsb.enroll_stage = 2  AND rsb.education_review_status = 2 and rsb.publicity_status = 1  then (1 + IFNULL(rti.tws,0))  else 0 end) as publicPrimaryAdmissionNum,
        <choose>
            <when test="deptCode == '130209'">
                sum(case when rsb.enroll_stage = 3 and rsb.is_transfer=0 AND rsb.education_review_status = 2 and rsb.publicity_status = 1  then (1 + IFNULL(rti.tws,0))  else 0 end) as publicJuniorAdmissionNum,
            </when>
            <otherwise>
                sum(case when rsb.enroll_stage = 3  AND rsb.education_review_status = 2 and rsb.publicity_status = 1  then (1 + IFNULL(rti.tws,0))  else 0 end) as publicJuniorAdmissionNum,
            </otherwise>
        </choose>
              0 as privatePrimaryNum,
            0 as privateJuniorNum,
            0 as privatePrimaryAdmissionNum,
            0 as privateJuniorAdmissionNum
            from `${dbName}`.r_student_base rsb
            left join
            (select student_id, count(1) tws from `${dbName}`.r_twins_info  GROUP BY student_id) rti
            ON rti.student_id = rsb.id
            WHERE
            rsb.delete_flag = 0 and rsb.adjustment = 0
    </select>

    <select id="getSpanStudentPublic" resultType="com.shida.user.center.pojo.vo.StudentStatisticsInfo">
        select t1.to_dept_id as dept_id,
        sum(case when t2.period = '2' and t1.school_type = '1'  then (1 + t1.tiwns_num)  else 0 end) as public_primary_num,
        sum(case when t2.period = '3' and t1.school_type = '1'  then (1 + t1.tiwns_num)  else 0 end) as public_junior_num,
		sum(case when t2.period = '2' and t1.school_type = '2'  then (1 + t1.tiwns_num)  else 0 end) as private_primary_num,
        sum(case when t2.period = '3' and t1.school_type = '2'  then (1 + t1.tiwns_num)  else 0 end) as private_junior_num
        from t_dept_student t1
        INNER JOIN t_dept t2 on t1.to_dept_id = t2.parent_id and t1.to_school_id = t2.id
        GROUP BY t1.to_dept_id
    </select>
    <select id="getDeptPeriod" resultType="java.lang.String">
        select period from t_dept where dept_name = #{deptName}
    </select>

</mapper>
