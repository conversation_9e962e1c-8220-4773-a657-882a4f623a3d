package com.shida.user.center.mapper;

import com.shida.user.center.entity.Dept;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.shida.user.center.pojo.vo.DeptInfo;
import com.shida.user.center.pojo.vo.StudentStatisticsInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 部门表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-22
 */
@Mapper
public interface DeptMapper extends BaseMapper<Dept> {

    DeptInfo selectDeptInfo(@Param("id") Long deptId);

    List<StudentStatisticsInfo> getDeptList();

    StudentStatisticsInfo getOtherDeptStudentStatistics(@Param("deptCode") String deptCode);

    StudentStatisticsInfo getPublicDeptStudentStatistics(@Param("deptCode") String deptCode, @Param("dbName") String dbName);

    List<StudentStatisticsInfo> getSpanStudentPublic();

    String getDeptPeriod(@Param("deptName") String deptName);
}
