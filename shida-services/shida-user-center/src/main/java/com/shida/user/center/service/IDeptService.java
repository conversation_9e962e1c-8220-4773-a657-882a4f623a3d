package com.shida.user.center.service;

import com.baomidou.mybatisplus.core.metadata.IPage;

import com.shida.pojo.vo.PageInfo;
import com.shida.user.center.entity.Dept;
import com.baomidou.mybatisplus.extension.service.IService;
import com.shida.user.center.pojo.excel.ExcelBatchImportResponse;
import com.shida.user.center.pojo.from.OpenTipsForm;
import com.shida.user.center.pojo.from.SchoolForm;

import com.shida.user.center.pojo.search.DeptSearch;
import com.shida.user.center.pojo.vo.DepParentInfo;
import com.shida.user.center.pojo.vo.DeptInfo;

import lombok.SneakyThrows;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

import java.util.List;

/**
 * <p>
 * 部门表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-22
 */
public interface IDeptService extends IService<Dept> {

    PageInfo<DeptInfo> getPageData(DeptSearch search);

    List<DeptInfo> getDataList(DeptSearch search, Boolean checkFlag);

    List<DepParentInfo> getParentList(DeptSearch search);

    DeptInfo getDeptDetail(Long deptId);

    Boolean createSchool(SchoolForm form);

    Boolean updateSchool(SchoolForm form);

    Boolean deleteById(Long deptId);

    Boolean changeDeptStatus(Long deptId, String status);

    Boolean changeOpenStatus(Long deptId);

    Boolean setOpenTips(OpenTipsForm form);

    List<ExcelBatchImportResponse> importSchool(MultipartFile file);
    List<ExcelBatchImportResponse> importSchools(MultipartFile file);
    String downTemplate(HttpServletResponse response);
    String downTemplates(HttpServletResponse response);

    @SneakyThrows
    String downSchoolRelacionTemplate(HttpServletResponse response);

    PageInfo<DeptInfo> getSchoolPageData(DeptSearch search);

    List<ExcelBatchImportResponse> importSchoolRelation(MultipartFile file);

    DeptInfo primaryJuniorDetail(Long key);
}
