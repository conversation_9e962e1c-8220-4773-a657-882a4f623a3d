package com.shida.user.center.controller;

import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.annotation.SaMode;
import com.shida.autolog.AutoLog;
import com.shida.autolog.OperateType;
import com.shida.constant.RoleConstant;
import com.shida.pojo.form.KeyForm;
import com.shida.pojo.vo.PageInfo;
import com.shida.user.center.pojo.excel.ExcelBatchImportResponse;
import com.shida.user.center.pojo.from.OpenTipsForm;
import com.shida.user.center.pojo.from.SchoolForm;
import com.shida.user.center.pojo.search.DeptSearch;
import com.shida.user.center.pojo.vo.DepParentInfo;
import com.shida.user.center.pojo.vo.DeptInfo;
import com.shida.user.center.service.IDeptService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/4/23 9:38
 * @PackageName:com.shida.user.center.controller
 * @ClassName: DeptController
 * @Version 1.0
 */
@Tag(name = "部门管理")
@RestController
@RequestMapping("/center/dept")
public class DeptController {

    @Resource
    private IDeptService deptService;

    @SaCheckRole(value = {RoleConstant.SUPER_ADMIN_CODE,RoleConstant.CITY_ADMIN_CODE,RoleConstant.COUNTY_ADMIN_CODE,RoleConstant.PARENT_CODE,RoleConstant.SCHOOL_CODE,RoleConstant.LOCATION}, mode = SaMode.OR)
    @Operation(summary = "获取部门分页数据")
    @PostMapping("pageList")
    public PageInfo<DeptInfo> pageList(@RequestBody DeptSearch search) {
        return deptService.getPageData(search);
    }

    @SaCheckRole(value = {RoleConstant.PARENT_CODE}, mode = SaMode.OR)
    @Operation(summary = "报名获取学校分页数据")
    @PostMapping("schoolPageList")
    public PageInfo<DeptInfo> schoolPageList(@RequestBody DeptSearch search) {
        return deptService.getSchoolPageData(search);
    }

    @SaCheckRole(value = {RoleConstant.SUPER_ADMIN_CODE,RoleConstant.CITY_ADMIN_CODE,RoleConstant.COUNTY_ADMIN_CODE,RoleConstant.SCHOOL_CODE,RoleConstant.AUDITOR_CODE,RoleConstant.LOCATION}, mode = SaMode.OR)
    @Operation(summary = "获取部门数据")
    @PostMapping("list")
    public List<DeptInfo> getList(@RequestBody DeptSearch search) {
        return deptService.getDataList(search, true);
    }

    @Operation(summary = "详情")
    @PostMapping("detail")
    public DeptInfo getDetail(@RequestBody @Valid KeyForm<Long> form){
        return deptService.getDeptDetail(form.getKey());
    }

    @Operation(summary = "小学对口初中详情")
    @PostMapping("primaryJuniorDetail")
    public DeptInfo getPrimaryJuniorDetail(@RequestBody @Valid KeyForm<Long> form){
        return deptService.primaryJuniorDetail(form.getKey());
    }

    @AutoLog(operateType = OperateType.OPERATE_ADD)
    @SaCheckRole(value = {RoleConstant.COUNTY_ADMIN_CODE}, mode = SaMode.OR)
    @Operation(summary = "新增学校")
    @PostMapping("createSchool")
    public Boolean createSchool(@RequestBody @Valid SchoolForm form) {
        return deptService.createSchool(form);
    }

    @AutoLog(operateType = OperateType.OPERATE_EDIT)
    @SaCheckRole(value = {RoleConstant.SUPER_ADMIN_CODE,RoleConstant.CITY_ADMIN_CODE,RoleConstant.COUNTY_ADMIN_CODE}, mode = SaMode.OR)
    @Operation(summary = "修改学校")
    @PostMapping("updateSchool")
    public Boolean updateSchool(@RequestBody @Valid SchoolForm form) {
        return deptService.updateSchool(form);
    }

    @AutoLog(operateType = OperateType.OPERATE_DELETE)
    @SaCheckRole(value = {RoleConstant.SUPER_ADMIN_CODE,RoleConstant.CITY_ADMIN_CODE,RoleConstant.COUNTY_ADMIN_CODE}, mode = SaMode.OR)
    @Operation(summary = "删除")
    @PostMapping("delete")
    public Boolean deleteDept(@RequestBody @Valid KeyForm<Long> form) {
        return deptService.deleteById(form.getKey());
    }

    @AutoLog(operateType = OperateType.OPERATE_DELETE)
    @SaCheckRole(value = {RoleConstant.SUPER_ADMIN_CODE,RoleConstant.CITY_ADMIN_CODE,RoleConstant.COUNTY_ADMIN_CODE}, mode = SaMode.OR)
    @Operation(summary = "修改状态")
    @PostMapping("changeStatusNormal")
    public Boolean changeStatusNormal(@RequestBody @Valid KeyForm<Long> form) {
        return deptService.changeDeptStatus(form.getKey(), "1");
    }

    @AutoLog(operateType = OperateType.OPERATE_EDIT)
    @SaCheckRole(value = {RoleConstant.SUPER_ADMIN_CODE,RoleConstant.CITY_ADMIN_CODE,RoleConstant.COUNTY_ADMIN_CODE}, mode = SaMode.OR)
    @Operation(summary = "修改状态")
    @PostMapping("changeStatusDisabled")
    public Boolean changeStatusDisabled(@RequestBody @Valid KeyForm<Long> form) {
        return deptService.changeDeptStatus(form.getKey(), "0");
    }

    @AutoLog(operateType = OperateType.OPERATE_IMPORT)
    @SaCheckRole(value = {RoleConstant.COUNTY_ADMIN_CODE}, mode = SaMode.OR)
    @Operation(summary = "批量导入")
    @PostMapping("/importSchool")
    public List<ExcelBatchImportResponse> importSchool(MultipartFile file) {
        return deptService.importSchool(file);
    }
    @AutoLog(operateType = OperateType.OPERATE_IMPORT)
    @SaCheckRole(value = {RoleConstant.COUNTY_ADMIN_CODE}, mode = SaMode.OR)
    @Operation(summary = "批量导入")
    @PostMapping("/importSchools")
    public List<ExcelBatchImportResponse> importSchools(MultipartFile file) {
        return deptService.importSchools(file);
    }

    @SaCheckRole(value = {RoleConstant.COUNTY_ADMIN_CODE}, mode = SaMode.OR)
    @Operation(summary = "学校导入模版下载")
    @PostMapping("/downTemplate")
    public void downTemplate(HttpServletResponse response) {
        deptService.downTemplate(response);
    }

    @SaCheckRole(value = {RoleConstant.COUNTY_ADMIN_CODE}, mode = SaMode.OR)
    @Operation(summary = "学校关系导入模版下载")
    @PostMapping("/downSchoolRelationTemplate")
    public void downSchoolRelationTemplate(HttpServletResponse response) {
        deptService.downSchoolRelacionTemplate(response);
    }
    @SaCheckRole(value = {RoleConstant.COUNTY_ADMIN_CODE}, mode = SaMode.OR)
    @Operation(summary = "学校导入模版下载")
    @PostMapping("/downTemplates")
    public void downTemplates(HttpServletResponse response) {
        deptService.downTemplates(response);
    }


    @AutoLog(operateType = OperateType.OPERATE_IMPORT)
    @SaCheckRole(value = {RoleConstant.COUNTY_ADMIN_CODE}, mode = SaMode.OR)
    @Operation(summary = "批量导入")
    @PostMapping("/importSchoolRelation")
    public List<ExcelBatchImportResponse> importSchoolRelation(MultipartFile file) {
        return deptService.importSchoolRelation(file);
    }


    @AutoLog(operateType = OperateType.OPERATE_EDIT)
    @SaCheckRole(value = {RoleConstant.SUPER_ADMIN_CODE}, mode = SaMode.OR)
    @Operation(summary = "修改按钮状态")
    @PostMapping("changeOpenStatus")
    public Boolean changeOpenStatus(@RequestBody @Valid KeyForm<Long> form) {
        return deptService.changeOpenStatus(form.getKey());
    }

    @AutoLog(operateType = OperateType.OPERATE_EDIT)
    @SaCheckRole(value = {RoleConstant.SUPER_ADMIN_CODE}, mode = SaMode.OR)
    @Operation(summary = "设置按钮提示信息")
    @PostMapping("setOpenTips")
    public Boolean setOpenTips(@RequestBody @Valid OpenTipsForm form) {
        return deptService.setOpenTips(form);
    }

    @Operation(summary = "家长获取部门数据")
    @PostMapping("deptList")
    public List<DepParentInfo> getParentList(@RequestBody DeptSearch search) {
        return deptService.getParentList(search);
    }
}
