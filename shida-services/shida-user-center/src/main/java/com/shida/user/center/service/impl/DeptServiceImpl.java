package com.shida.user.center.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.shida.config.IdGenerator;
import com.shida.constant.RedisKeyConstant;
import com.shida.constant.RoleConstant;
import com.shida.enums.RestCode;
import com.shida.excel.ExcelHelper;
import com.shida.exceptions.ServiceException;
import com.shida.pojo.vo.AccountInfo;
import com.shida.pojo.vo.PageInfo;
import com.shida.user.center.entity.*;
import com.shida.user.center.enums.SchoolTypeCode;
import com.shida.user.center.enums.NatureCode;
import com.shida.user.center.enums.PeriodCode;
import com.shida.user.center.enums.TypeCode;
import com.shida.user.center.mapper.DeptMapper;
import com.shida.user.center.mapper.PrimaryJuniorMapper;
import com.shida.user.center.mapper.SchoolRangMapper;
import com.shida.user.center.mapper.UserMapper;
import com.shida.user.center.pojo.excel.*;
import com.shida.user.center.pojo.from.OpenTipsForm;
import com.shida.user.center.pojo.from.SchoolForm;
import com.shida.user.center.pojo.search.DeptSearch;
import com.shida.user.center.pojo.search.SchoolRangSearch;
import com.shida.user.center.pojo.vo.DepParentInfo;
import com.shida.user.center.pojo.vo.DeptInfo;
import com.shida.user.center.pojo.vo.SchoolRangInfo;
import com.shida.user.center.service.IDeptService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shida.user.center.service.IPrimaryJuniorService;
import com.shida.user.center.service.ISchoolContentService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 部门表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-22
 */
@Service
@Slf4j
public class DeptServiceImpl extends ServiceImpl<DeptMapper, Dept> implements IDeptService {

    @Resource
    private ISchoolContentService schoolContentService;
    @Resource
    private UserMapper userMapper;
    @Resource
    private SchoolRangMapper schoolRangMapper;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    private IdGenerator idGenerator;
    @Resource
    private PrimaryJuniorMapper primaryJuniorMapper;

    private static final String PARENT_DEPT_STATUS = "parent:dept:status";

    @Override
    public PageInfo<DeptInfo> getPageData(DeptSearch search) {
        Map<Long, String> rangMap = new HashMap<>();
        AccountInfo info = (AccountInfo) redisTemplate.opsForValue().get(RedisKeyConstant.CURRENT_USER_INFO + StpUtil.getTokenValue());
        if (RoleConstant.CITY_ADMIN_CODE.equals(info.getRoleCode())) {
            search.setAncestors("0,");
        }
        if (RoleConstant.COUNTY_ADMIN_CODE.equals(info.getRoleCode())) {
            search.setAncestors("0,1," + info.getDeptId());
        }
        if (RoleConstant.SCHOOL_CODE.equals(info.getRoleCode())) {
            search.setLevel(3);
            search.setAncestors("0,1," + info.getDeptInfo().getParentId());
        }
        if (RoleConstant.PARENT_CODE.equals(info.getRoleCode())) {
            search.setLevel(3);
            search.setStatus("1");
            Dept dept = baseMapper.selectOne(new LambdaQueryWrapper<Dept>().eq(Dept::getDeptCode, search.getDeptCode()));
            if (Objects.nonNull(dept)) {
                search.setParentId(dept.getId());
            } else {
                throw new SecurityException("请先选择区县");
            }
            if (Objects.isNull(search.getParentId())) {
                throw new SecurityException("请先选择区县");
            }
            SchoolRangSearch schoolRangSearch = new SchoolRangSearch();
            schoolRangSearch.setCountyId(search.getParentId());
            List<SchoolRangInfo> rangs = schoolRangMapper.getInfoListDataOne(schoolRangSearch);
            rangMap = rangs != null && rangs.size() > 0 ?
                    rangs.stream().collect(Collectors.toMap(SchoolRangInfo::getSchoolId, SchoolRangInfo::getRangeName)) : new HashMap<>();
        }
        IPage<Dept> page = new Page<Dept>(search.getPageNumber(), search.getPageSize());
        LambdaQueryWrapper<Dept> queryWrapper = new LambdaQueryWrapper<Dept>();

        // 检查是否为区县转学模式
        if (search.getIsTransferMode() != null && search.getIsTransferMode()) {
            // 区县转学模式，使用简化查询条件，不使用type和nature筛选
            queryWrapper.like(StrUtil.isNotBlank(search.getKeywords()), Dept::getDeptName, search.getKeywords())
                    .likeRight(StrUtil.isNotBlank(search.getAncestors()), Dept::getAncestors, search.getAncestors())
                    .eq(StrUtil.isNotBlank(search.getPeriod()), Dept::getPeriod, search.getPeriod())
                    .eq(search.getParentId() != null, Dept::getParentId, search.getParentId())
                    .eq(search.getLevel() != null, Dept::getLevel, search.getLevel())
                    .eq(StrUtil.isNotBlank(search.getStatus()), Dept::getStatus, search.getStatus())
                    .orderByAsc(Dept::getSort)
                    .orderByAsc(Dept::getId);
        } else {
            // 常规模式，使用完整查询条件
            queryWrapper.like(StrUtil.isNotBlank(search.getKeywords()), Dept::getDeptName, search.getKeywords())
                    .likeRight(StrUtil.isNotBlank(search.getAncestors()), Dept::getAncestors, search.getAncestors())
                    .eq(StrUtil.isNotBlank(search.getType()), Dept::getType, search.getType())
                    .eq(StrUtil.isNotBlank(search.getPeriod()), Dept::getPeriod, search.getPeriod())
                    .eq(StrUtil.isNotBlank(search.getIsFive()), Dept::getIsFive, search.getIsFive())
                    .eq(StrUtil.isNotBlank(search.getNature()), Dept::getNature, search.getNature())
                    .eq(search.getParentId() != null, Dept::getParentId, search.getParentId())
                    .eq(search.getLevel() != null, Dept::getLevel, search.getLevel())
                    .eq(StrUtil.isNotBlank(search.getStatus()), Dept::getStatus, search.getStatus())
                    .orderByAsc(Dept::getSort)
                    .orderByAsc(Dept::getId);
        }

        page = super.page(page, queryWrapper);
        PageInfo<DeptInfo> result = PageInfo.fromMybatisPage(page, DeptInfo.class);
        if (!Objects.isNull(result.getRecords()) && RoleConstant.PARENT_CODE.equals(info.getRoleCode())) {
            for (DeptInfo deptInfo : result.getRecords()) {
                deptInfo.setArea(rangMap.containsKey(deptInfo.getId()) ? rangMap.get(deptInfo.getId()) : null);
            }
        }
        return result;
    }

    @Override
    public List<DeptInfo> getDataList(DeptSearch search, Boolean checkFlag) {
        if (checkFlag) {
            AccountInfo info = (AccountInfo) redisTemplate.opsForValue().get(RedisKeyConstant.CURRENT_USER_INFO + StpUtil.getTokenValue());
            if (RoleConstant.CITY_ADMIN_CODE.equals(info.getRoleCode())) {
                search.setAncestors("0,");
            }
            if (RoleConstant.COUNTY_ADMIN_CODE.equals(info.getRoleCode())) {
                search.setAncestors("0,1," + info.getDeptId());
            }
            if (RoleConstant.AUDITOR_CODE.equals(info.getRoleCode())) {
                search.setAncestors("0,1," + info.getDeptId());
            }
        }

        LambdaQueryWrapper<Dept> queryWrapper = new LambdaQueryWrapper<Dept>();
        queryWrapper.like(StrUtil.isNotBlank(search.getKeywords()), Dept::getDeptName, search.getKeywords())
                .likeRight(StrUtil.isNotBlank(search.getAncestors()), Dept::getAncestors, search.getAncestors())
                .eq(StrUtil.isNotBlank(search.getType()), Dept::getType, search.getType())
                .eq(StrUtil.isNotBlank(search.getPeriod()), Dept::getPeriod, search.getPeriod())
                .eq(StrUtil.isNotBlank(search.getIsFive()), Dept::getIsFive, search.getIsFive())
                .eq(StrUtil.isNotBlank(search.getNature()), Dept::getNature, search.getNature())
                .eq(search.getParentId() != null, Dept::getParentId, search.getParentId())
                .eq(search.getDeptId() != null, Dept::getId, search.getDeptId())
                .eq(search.getLevel() != null, Dept::getLevel, search.getLevel())
                .eq(StrUtil.isNotBlank(search.getStatus()), Dept::getStatus, search.getStatus())
                .orderByAsc(Dept::getSort)
                .orderByAsc(Dept::getId);
        List<Dept> list = super.list(queryWrapper);
        List<DeptInfo> result = BeanUtil.copyToList(list, DeptInfo.class);
        return result;
    }

    List<DepParentInfo> deptList;

    @PostConstruct
    public void init() {
        LambdaQueryWrapper<Dept> queryWrapper = new LambdaQueryWrapper<Dept>();
        queryWrapper
                .eq(Dept::getLevel, 2)
                .eq(Dept::getStatus, 1)
                .orderByAsc(Dept::getSort)
                .orderByAsc(Dept::getId);
        List<Dept> list = super.list(queryWrapper);
        deptList = new ArrayList<>();
        deptList = BeanUtil.copyToList(list, DepParentInfo.class);
        redisTemplate.opsForValue().set(PARENT_DEPT_STATUS, deptList);
    }


    @Override
    public List<DepParentInfo> getParentList(DeptSearch search) {
        System.out.println("aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa" + JSONUtil.toJsonStr(deptList));
        return deptList;
    }

    @Override
    public DeptInfo getDeptDetail(Long deptId) {
        DeptInfo result = baseMapper.selectDeptInfo(deptId);
        if (Objects.isNull(result)) {
            throw new SecurityException("未找到");
        }
        if (result.getLevel().intValue() == 3) {

            List<SchoolRangInfo> rangInfos = schoolRangMapper.getInfoListDataOne(new SchoolRangSearch().setSchoolId(deptId));
            if (rangInfos != null && rangInfos.size() > 0) {
                result.setArea(rangInfos.get(0).getRangeName());
            }
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = SecurityException.class)
    public Boolean createSchool(SchoolForm form) {
        Dept dept = new Dept();
        BeanUtil.copyProperties(form, dept);
        AccountInfo info = (AccountInfo) redisTemplate.opsForValue().get(RedisKeyConstant.CURRENT_USER_INFO + StpUtil.getTokenValue());
        if (RoleConstant.CITY_ADMIN_CODE.equals(info.getRoleCode())) {
            Dept county = this.getById(form.getParentId());
            dept.setAncestors("0,1," + county.getId());
            dept.setParentId(county.getId());
            dept.setParentName(county.getDeptName());
            dept.setAncestorsName("河北省,唐山市," + county.getDeptName());
        }
        if (RoleConstant.COUNTY_ADMIN_CODE.equals(info.getRoleCode())) {
            Dept county = this.getById(info.getDeptId());
            dept.setAncestors("0,1," + county.getId());
            dept.setParentId(county.getId());
            dept.setParentName(county.getDeptName());
            dept.setAncestorsName("河北省,唐山市," + county.getDeptName());
        }
        if(ObjectUtil.isNotEmpty(form.getSchoolBiaoShi())){
            dept.setSchoolBiaoShi(form.getSchoolBiaoShi());
        }
        dept.setLevel(3);
        dept.setStatus("1");
        super.save(dept);
        //保存学校风采
        if (StrUtil.isNotBlank(form.getContent())) {
            SchoolContent schoolContent = new SchoolContent();
            schoolContent.setContent(form.getContent()).setSchoolId(dept.getId());
            schoolContentService.save(schoolContent);
        }
        redisTemplate.delete(PARENT_DEPT_STATUS);
        return true;
    }


    @Override
    @Transactional(rollbackFor = SecurityException.class)
    public Boolean updateSchool(SchoolForm form) {
        Dept oldDept = baseMapper.selectById(form.getId());
        if (Objects.isNull(oldDept)) {
            throw new SecurityException("未找到记录");
        }
        AccountInfo info = (AccountInfo) redisTemplate.opsForValue().get(RedisKeyConstant.CURRENT_USER_INFO + StpUtil.getTokenValue());
        if (RoleConstant.COUNTY_ADMIN_CODE.equals(info.getRoleCode())) {
            if (!oldDept.getParentId().equals(info.getDeptId())) {
                throw new SecurityException("权限不足");
            }
        }
        Dept county = this.getById(oldDept.getParentId());
        //根据学校所属区县，查询开始报名时间
        //区县信息
//        Dept countyDept = baseMapper.selectById(oldDept.getId());


        //需要验证各区报名时间
        Dept deptNew = new Dept();
        BeanUtil.copyProperties(form, deptNew);
        deptNew.setLevel(3);
        if(ObjectUtil.isNotEmpty(form.getSchoolBiaoShi())){
            deptNew.setSchoolBiaoShi(form.getSchoolBiaoShi());
        }
        deptNew.setParentId(oldDept.getParentId());
        deptNew.setAncestorsName(oldDept.getAncestorsName());
        deptNew.setAncestors(oldDept.getAncestors());
        deptNew.setParentName(oldDept.getParentName());
        //保存学校风采
        if (StrUtil.isNotBlank(form.getContent())) {
            SchoolContent schoolContent = schoolContent = schoolContentService.getOne(new LambdaQueryWrapper<SchoolContent>()
                    .eq(SchoolContent::getSchoolId, deptNew.getId()));
            if (Objects.isNull(schoolContent)) {
                schoolContent = new SchoolContent();
                schoolContent.setContent(form.getContent()).setSchoolId(deptNew.getId());
                schoolContentService.save(schoolContent);
            } else {
                schoolContentService.update(new LambdaUpdateWrapper<SchoolContent>()
                        .set(SchoolContent::getContent, form.getContent())
                        .eq(SchoolContent::getSchoolId, deptNew.getId()));
            }
        }
        super.updateById(deptNew);
        redisTemplate.delete(PARENT_DEPT_STATUS);
        return true;
    }

    @Override
    @Transactional(rollbackFor = SecurityException.class)
    public Boolean deleteById(Long deptId) {
        Dept dept = baseMapper.selectById(deptId);
        if (Objects.isNull(dept)) {
            throw new SecurityException("未找到记录");
        }
        AccountInfo info = (AccountInfo) redisTemplate.opsForValue().get(RedisKeyConstant.CURRENT_USER_INFO + StpUtil.getTokenValue());
        if (RoleConstant.COUNTY_ADMIN_CODE.equals(info.getRoleCode())) {
            if (!dept.getParentId().equals(info.getDeptId())) {
                throw new SecurityException("权限不足");
            }
        }
        //学校
        if (dept.getLevel() == 3) {
            Long ucount = userMapper.selectCount(new LambdaQueryWrapper<User>().eq(User::getDeptId, dept.getId()));
            if (ucount.intValue() > 0) {
                throw new SecurityException("还有用户存在，无法删除");
            }
        }
        //区县
        else if (dept.getLevel() == 2) {
            List<Dept> depts = baseMapper.selectList(new LambdaQueryWrapper<Dept>().eq(Dept::getParentId, deptId));
            if (depts != null && depts.size() > 0) {
                throw new SecurityException("还有学校存在，无法删除");
            }
            Long ucount = userMapper.selectCount(new LambdaQueryWrapper<User>().eq(User::getDeptId, dept.getId()));
            if (ucount.intValue() > 0) {
                throw new SecurityException("还有用户存在，无法删除");
            }
        } else {
            throw new SecurityException("无法删除");
        }
        super.removeById(deptId);
        redisTemplate.delete(PARENT_DEPT_STATUS);
        return true;
    }

    @Override
    @Transactional(rollbackFor = SecurityException.class)
    public Boolean changeDeptStatus(Long deptId, String status) {
        Dept dept = baseMapper.selectById(deptId);
        if (Objects.isNull(dept)) {
            throw new SecurityException("未找到记录");
        }
        AccountInfo info = (AccountInfo) redisTemplate.opsForValue().get(RedisKeyConstant.CURRENT_USER_INFO + StpUtil.getTokenValue());
        if (RoleConstant.COUNTY_ADMIN_CODE.equals(info.getRoleCode())) {
            if (!dept.getParentId().equals(info.getDeptId())) {
                throw new SecurityException("权限不足");
            }
        }
        dept.setStatus(status);
        super.updateById(dept);
        redisTemplate.delete(PARENT_DEPT_STATUS);
        return true;
    }

    @Override
    @Transactional(rollbackFor = SecurityException.class)
    public Boolean changeOpenStatus(Long deptId) {
        Dept dept = baseMapper.selectById(deptId);
        if (Objects.isNull(dept)) {
            throw new SecurityException("未找到记录");
        }
        dept.setOpenStatus(1 == dept.getOpenStatus().intValue() ? 0 : 1);
        super.updateById(dept);
        redisTemplate.delete(PARENT_DEPT_STATUS);
        return true;
    }

    @Override
    @Transactional(rollbackFor = SecurityException.class)
    public Boolean setOpenTips(OpenTipsForm form) {
        Dept dept = baseMapper.selectById(form.getId());
        if (Objects.isNull(dept)) {
            throw new SecurityException("未找到记录");
        }
        dept.setTips(form.getTips());
        dept.setPcUrl(form.getPcUrl());
        dept.setWxUrl(form.getWxUrl());
        super.updateById(dept);
        redisTemplate.delete(PARENT_DEPT_STATUS);
        return true;
    }


    @Override
    @Transactional(rollbackFor = SecurityException.class)
    public List<ExcelBatchImportResponse> importSchool(MultipartFile file) {
        AccountInfo info = (AccountInfo) redisTemplate.opsForValue().get(RedisKeyConstant.CURRENT_USER_INFO + StpUtil.getTokenValue());
        String deptCode = info.getDeptCode();
        Long deptId = info.getDeptId();
        List<ExcelBatchImportResponse> errorMessage = new ArrayList<>();
        if (Objects.nonNull(file)) {
            try {
                List<SchoolExcel> excelDataList = new ArrayList<>();
                ExcelHelper<SchoolExcel> excelHelper = ExcelHelper.create(SchoolExcel.class);
                excelDataList = BeanUtil.copyToList(excelHelper.readData(file.getInputStream()), SchoolExcel.class);
                if (excelDataList.size() > 0) {
                    errorMessage = this.checkExcelData(excelDataList);
                    if (errorMessage.size() > 0) {
                        return errorMessage;
                    }
                    //处理数据
                    List<Dept> deptList = this.manageExcelData(excelDataList);
                    int pageNumber = 0, pageSize = 500, total = 0;
                    do {
                        try {
                            List<Dept> scoreList = new ArrayList<>();
                            List<Dept> dataList = deptList.stream().skip((long) pageNumber * pageSize).limit(pageSize).collect(Collectors.toList());
                            for (Dept excel : dataList) {
                                scoreList.add(excel);
                                total++;
                            }
                            //保存
                            super.saveBatch(scoreList);
                            scoreList.clear();
                            pageNumber++;
                        } catch (Exception e) {
                            errorMessage.add(new ExcelBatchImportResponse().setRowIndex(total).setMessage(e.getMessage()));
                            throw e;
                        }
                    } while (total < excelDataList.size());
                } else {
                    errorMessage.add(new ExcelBatchImportResponse().setMessage("未检测到数据").setRowIndex(1));
                }
            } catch (Exception e) {
                throw new SecurityException("文件错误");
            }
        }
        redisTemplate.delete(PARENT_DEPT_STATUS);
        return errorMessage;
    }
    @Override
    @Transactional(rollbackFor = SecurityException.class)
    public List<ExcelBatchImportResponse> importSchools(MultipartFile file) {
        AccountInfo info = (AccountInfo) redisTemplate.opsForValue().get(RedisKeyConstant.CURRENT_USER_INFO + StpUtil.getTokenValue());
        String deptCode = info.getDeptCode();
        Long deptId = info.getDeptId();
        List<ExcelBatchImportResponse> errorMessage = new ArrayList<>();
        if (Objects.nonNull(file)) {
            try {
                List<SchoolExcels> excelDataList = new ArrayList<>();
                ExcelHelper<SchoolExcels> excelHelper = ExcelHelper.create(SchoolExcels.class);
                excelDataList = BeanUtil.copyToList(excelHelper.readData(file.getInputStream()), SchoolExcels.class);
                if (excelDataList.size() > 0) {
                    errorMessage = this.checkExcelDatas(excelDataList);
                    if (errorMessage.size() > 0) {
                        return errorMessage;
                    }
                    //处理数据
                    List<Dept> deptList = this.manageExcelDatas(excelDataList);
                    int pageNumber = 0, pageSize = 500, total = 0;
                    do {
                        try {
                            List<Dept> scoreList = new ArrayList<>();
                            List<Dept> dataList = deptList.stream().skip((long) pageNumber * pageSize).limit(pageSize).collect(Collectors.toList());
                            for (Dept excel : dataList) {
                                scoreList.add(excel);
                                total++;
                            }
                            //保存
                            super.saveBatch(scoreList);
                            scoreList.clear();
                            pageNumber++;
                        } catch (Exception e) {
                            errorMessage.add(new ExcelBatchImportResponse().setRowIndex(total).setMessage(e.getMessage()));
                            throw e;
                        }
                    } while (total < excelDataList.size());
                } else {
                    errorMessage.add(new ExcelBatchImportResponse().setMessage("未检测到数据").setRowIndex(1));
                }
            } catch (Exception e) {
                throw new SecurityException("文件错误");
            }
        }
        redisTemplate.delete(PARENT_DEPT_STATUS);
        return errorMessage;
    }

    private List<Dept> manageExcelData(List<SchoolExcel> excelDataList) {
        List<Dept> result = new ArrayList<>();
        AccountInfo info = (AccountInfo) redisTemplate.opsForValue().get(RedisKeyConstant.CURRENT_USER_INFO + StpUtil.getTokenValue());
        String deptCode = info.getDeptCode();
        for (SchoolExcel excel : excelDataList) {
            Dept dept = new Dept();
            dept = BeanUtil.copyProperties(excel, Dept.class);
            dept.setId(idGenerator.getNextId());
            dept.setParentName(info.getDeptName());
            dept.setParentId(info.getDeptId());
            dept.setStatus("1");
            dept.setLevel(3);
            dept.setAncestors("0,1," + info.getDeptId());
            dept.setEnrollType("A,B,C,D,E,F,G,H");
            dept.setAncestorsName(info.getDeptInfo().getAncestorsName() + "," + info.getDeptName());
            dept.setDeptName(StrUtil.trim(dept.getDeptName()).replaceAll(" ", ""));
            //公办
            dept.setType(TypeCode.PUBLIC.getValue().toString());
            //办学性质（1乡镇2城区）
            if (NatureCode.TOWN.getDescription().equals(excel.getNatureStr())) {
                dept.setNature(NatureCode.TOWN.getValue().toString());
            } else if (NatureCode.CITY.getDescription().equals(excel.getNatureStr())) {
                dept.setNature(NatureCode.CITY.getValue().toString());
            } else if(NatureCode.NEW_CITY.getDescription().equals(excel.getNatureStr())){
                dept.setNature(NatureCode.NEW_CITY.getValue().toString());
            } else if(NatureCode.INDUSTRIAL_CITY.getDescription().equals(excel.getNatureStr())){
                dept.setNature(NatureCode.INDUSTRIAL_CITY.getValue().toString());
            }
            //学校类型（1公办2民办）
            if (SchoolTypeCode.PRIVATE.getDescription().equals(excel.getTypeStr())) {
                dept.setType(SchoolTypeCode.PRIVATE.getValue().toString());
            } else if (SchoolTypeCode.PUBLIC.getDescription().equals(excel.getTypeStr())) {
                dept.setType(SchoolTypeCode.PUBLIC.getValue().toString());
            }
            //学段
            if (PeriodCode.PRESCHOOL.getDescription().equals(excel.getPeriodStr())) {
                dept.setPeriod(PeriodCode.PRESCHOOL.getValue().toString());
            } else if (PeriodCode.PRIMARY.getDescription().equals(excel.getPeriodStr())) {
                dept.setPeriod(PeriodCode.PRIMARY.getValue().toString());
            } else if (PeriodCode.JUNIOR.getDescription().equals(excel.getPeriodStr())) {
                dept.setPeriod(PeriodCode.JUNIOR.getValue().toString());
            }
            result.add(dept);
        }
        return result;
    }
    private List<Dept> manageExcelDatas(List<SchoolExcels> excelDataList) {
        List<Dept> result = new ArrayList<>();
        AccountInfo info = (AccountInfo) redisTemplate.opsForValue().get(RedisKeyConstant.CURRENT_USER_INFO + StpUtil.getTokenValue());
        String deptCode = info.getDeptCode();
        for (SchoolExcels excel : excelDataList) {
            Dept dept = new Dept();
            dept = BeanUtil.copyProperties(excel, Dept.class);
            dept.setId(idGenerator.getNextId());
            dept.setParentName(info.getDeptName());
            dept.setParentId(info.getDeptId());
            dept.setStatus("1");
            dept.setLevel(3);
            dept.setAncestors("0,1," + info.getDeptId());
            dept.setEnrollType("A,B,C,D,E,F,G,H");
            dept.setAncestorsName(info.getDeptInfo().getAncestorsName() + "," + info.getDeptName());
            dept.setDeptName(StrUtil.trim(dept.getDeptName()).replaceAll(" ", ""));
            //公办
            dept.setType(TypeCode.PUBLIC.getValue().toString());
            //办学性质（1乡镇2城区）
            if (NatureCode.TOWN.getDescription().equals(excel.getNatureStr())) {
                dept.setNature(NatureCode.TOWN.getValue().toString());
            } else if (NatureCode.CITY.getDescription().equals(excel.getNatureStr())) {
                dept.setNature(NatureCode.CITY.getValue().toString());
            } else if(NatureCode.NEW_CITY.getDescription().equals(excel.getNatureStr())){
                dept.setNature(NatureCode.NEW_CITY.getValue().toString());
            } else if(NatureCode.INDUSTRIAL_CITY.getDescription().equals(excel.getNatureStr())){
                dept.setNature(NatureCode.INDUSTRIAL_CITY.getValue().toString());
            }
            //学校类型（1公办2民办）
            if (SchoolTypeCode.PRIVATE.getDescription().equals(excel.getTypeStr())) {
                dept.setType(SchoolTypeCode.PRIVATE.getValue().toString());
            } else if (SchoolTypeCode.PUBLIC.getDescription().equals(excel.getTypeStr())) {
                dept.setType(SchoolTypeCode.PUBLIC.getValue().toString());
            }
            //学段
            if (PeriodCode.PRESCHOOL.getDescription().equals(excel.getPeriodStr())) {
                dept.setPeriod(PeriodCode.PRESCHOOL.getValue().toString());
            } else if (PeriodCode.PRIMARY.getDescription().equals(excel.getPeriodStr())) {
                dept.setPeriod(PeriodCode.PRIMARY.getValue().toString());
            } else if (PeriodCode.JUNIOR.getDescription().equals(excel.getPeriodStr())) {
                dept.setPeriod(PeriodCode.JUNIOR.getValue().toString());
            }
            result.add(dept);
        }
        return result;
    }

    private List<ExcelBatchImportResponse> checkExcelData(List<SchoolExcel> excelDataList) {
        List<ExcelBatchImportResponse> errorMessage = new ArrayList<>();
        int pageNumber = 0, pageSize = 500, total = 0;
        AccountInfo info = (AccountInfo) redisTemplate.opsForValue().get(RedisKeyConstant.CURRENT_USER_INFO + StpUtil.getTokenValue());
        String deptCode = info.getDeptCode();
        String parentName = info.getDeptName();
        DeptSearch deptSearch = new DeptSearch();
        deptSearch.setLevel(3);
        List<DeptInfo> schoolList = this.getDataList(deptSearch, false);
        Map<String, DeptInfo> schoolMap = schoolList != null && schoolList.size() > 0 ?
                schoolList.stream().collect(Collectors.toMap(DeptInfo::getDeptName, i -> i)) : new HashMap<>();
        Map<String, Integer> repeatMap = new HashMap<>();
        do {
            List<SchoolExcel> dataList = excelDataList.stream().skip((long) pageNumber * pageSize).limit(pageSize).collect(Collectors.toList());
            String errorText = "学校名称%s，%s";
            for (SchoolExcel excel : dataList) {
                if (Objects.isNull(excel.getDeptName())) {
                    errorMessage.add(new ExcelBatchImportResponse().setRowIndex(total + 1)
                            .setMessage("学校名称不能为空"));
                }
                if (Objects.isNull(excel.getNatureStr())) {
                    errorMessage.add(new ExcelBatchImportResponse().setRowIndex(total + 1)
                            .setMessage("学校性质不能为空"));
                }
                if (Objects.isNull(excel.getTypeStr())) {
                    errorMessage.add(new ExcelBatchImportResponse().setRowIndex(total + 1)
                            .setMessage("学校类型不能为空"));
                }
                if (Objects.isNull(excel.getPeriodStr())) {
                    errorMessage.add(new ExcelBatchImportResponse().setRowIndex(total + 1)
                            .setMessage("学段不能为空"));
                }
                if (Objects.isNull(excel.getAddress())) {
                    errorMessage.add(new ExcelBatchImportResponse().setRowIndex(total + 1)
                            .setMessage("地址不能为空"));
                }
                if (Objects.isNull(excel.getMaster())) {
                    errorMessage.add(new ExcelBatchImportResponse().setRowIndex(total + 1)
                            .setMessage("联系人不能为空"));
                }
                if (Objects.isNull(excel.getMobile())) {
                    errorMessage.add(new ExcelBatchImportResponse().setRowIndex(total + 1)
                            .setMessage("联系电话不能为空"));
                }

                if (Objects.isNull(excel.getPlanNum())) {
                    errorMessage.add(new ExcelBatchImportResponse().setRowIndex(total + 1)
                            .setMessage("招生计划数不能为空"));
                }
                //学校重名
                if (schoolMap.containsKey(excel.getDeptName())) {
                    DeptInfo school = schoolMap.get(excel.getDeptName());
                    if (!parentName.equals(school.getParentName())) {
                        errorMessage.add(new ExcelBatchImportResponse().setRowIndex(total + 1)
                                .setMessage(String.format(errorText, excel.getDeptName(), "在其他区县已经存在")));
                    } else {
                        errorMessage.add(new ExcelBatchImportResponse().setRowIndex(total + 1)
                                .setMessage(String.format(errorText, excel.getDeptName(), "本区县已经添加")));
                    }
                }
                if (repeatMap.containsKey(excel.getDeptName())) {
                    errorMessage.add(new ExcelBatchImportResponse().setRowIndex(total + 1)
                            .setMessage(String.format(errorText, excel.getDeptName(), "名称重复")));
                } else {
                    repeatMap.put(excel.getDeptName(), total + 1);
                }
                //学段
                if (PeriodCode.PRESCHOOL.getDescription().equals(excel.getPeriodStr()) || PeriodCode.PRIMARY.getDescription().equals(excel.getPeriodStr())
                        || PeriodCode.JUNIOR.getDescription().equals(excel.getPeriodStr())) {

                } else {
                    errorMessage.add(new ExcelBatchImportResponse().setRowIndex(total + 1)
                            .setMessage(String.format(errorText, excel.getDeptName(), "学段不对")));
                }
                //学校性质
                if (NatureCode.TOWN.getDescription().equals(excel.getNatureStr()) || NatureCode.CITY.getDescription().equals(excel.getNatureStr()) || NatureCode.NEW_CITY.getDescription().equals(excel.getNatureStr())|| NatureCode.INDUSTRIAL_CITY.getDescription().equals(excel.getNatureStr())) {

                } else {
                    errorMessage.add(new ExcelBatchImportResponse().setRowIndex(total + 1)
                            .setMessage(String.format(errorText, excel.getDeptName(), "学校性质不对")));
                }
                //学校类型
                if (SchoolTypeCode.PUBLIC.getDescription().equals(excel.getTypeStr()) || SchoolTypeCode.PRIVATE.getDescription().equals(excel.getTypeStr())) {

                } else {
                    errorMessage.add(new ExcelBatchImportResponse().setRowIndex(total + 1)
                            .setMessage(String.format(errorText, excel.getDeptName(), "学校类型不对")));
                }
                total++;
            }

            pageNumber++;
        } while (total < excelDataList.size());
        return errorMessage;
    }

    private List<ExcelBatchImportResponse> checkExcelDatas(List<SchoolExcels> excelDataList) {
        List<ExcelBatchImportResponse> errorMessage = new ArrayList<>();
        int pageNumber = 0, pageSize = 500, total = 0;
        AccountInfo info = (AccountInfo) redisTemplate.opsForValue().get(RedisKeyConstant.CURRENT_USER_INFO + StpUtil.getTokenValue());
        String deptCode = info.getDeptCode();
        String parentName = info.getDeptName();
        DeptSearch deptSearch = new DeptSearch();
        deptSearch.setLevel(3);
        List<DeptInfo> schoolList = this.getDataList(deptSearch, false);
        Map<String, DeptInfo> schoolMap = schoolList != null && schoolList.size() > 0 ?
                schoolList.stream().collect(Collectors.toMap(DeptInfo::getDeptName, i -> i)) : new HashMap<>();
        Map<String, Integer> repeatMap = new HashMap<>();
        do {
            List<SchoolExcels> dataList = excelDataList.stream().skip((long) pageNumber * pageSize).limit(pageSize).collect(Collectors.toList());
            String errorText = "学校名称%s，%s";
            for (SchoolExcels excel : dataList) {
                if (Objects.isNull(excel.getDeptName())) {
                    errorMessage.add(new ExcelBatchImportResponse().setRowIndex(total + 1)
                            .setMessage("学校名称不能为空"));
                }
                if (Objects.isNull(excel.getNatureStr())) {
                    errorMessage.add(new ExcelBatchImportResponse().setRowIndex(total + 1)
                            .setMessage("学校性质不能为空"));
                }
                if (Objects.isNull(excel.getTypeStr())) {
                    errorMessage.add(new ExcelBatchImportResponse().setRowIndex(total + 1)
                            .setMessage("学校类型不能为空"));
                }
                if (Objects.isNull(excel.getPeriodStr())) {
                    errorMessage.add(new ExcelBatchImportResponse().setRowIndex(total + 1)
                            .setMessage("学段不能为空"));
                }
                if (Objects.isNull(excel.getAddress())) {
                    errorMessage.add(new ExcelBatchImportResponse().setRowIndex(total + 1)
                            .setMessage("地址不能为空"));
                }
                if (Objects.isNull(excel.getMaster())) {
                    errorMessage.add(new ExcelBatchImportResponse().setRowIndex(total + 1)
                            .setMessage("联系人不能为空"));
                }
                if (Objects.isNull(excel.getMobile())) {
                    errorMessage.add(new ExcelBatchImportResponse().setRowIndex(total + 1)
                            .setMessage("联系电话不能为空"));
                }

                if (Objects.isNull(excel.getPlanNum())) {
                    errorMessage.add(new ExcelBatchImportResponse().setRowIndex(total + 1)
                            .setMessage("招生计划数不能为空"));
                }
                //学校重名
                if (schoolMap.containsKey(excel.getDeptName())) {
                    DeptInfo school = schoolMap.get(excel.getDeptName());
                    if (!parentName.equals(school.getParentName())) {
                        errorMessage.add(new ExcelBatchImportResponse().setRowIndex(total + 1)
                                .setMessage(String.format(errorText, excel.getDeptName(), "在其他区县已经存在")));
                    } else {
                        errorMessage.add(new ExcelBatchImportResponse().setRowIndex(total + 1)
                                .setMessage(String.format(errorText, excel.getDeptName(), "本区县已经添加")));
                    }
                }
                if (repeatMap.containsKey(excel.getDeptName())) {
                    errorMessage.add(new ExcelBatchImportResponse().setRowIndex(total + 1)
                            .setMessage(String.format(errorText, excel.getDeptName(), "名称重复")));
                } else {
                    repeatMap.put(excel.getDeptName(), total + 1);
                }
                //学段
                if (PeriodCode.PRESCHOOL.getDescription().equals(excel.getPeriodStr()) || PeriodCode.PRIMARY.getDescription().equals(excel.getPeriodStr())
                        || PeriodCode.JUNIOR.getDescription().equals(excel.getPeriodStr())) {

                } else {
                    errorMessage.add(new ExcelBatchImportResponse().setRowIndex(total + 1)
                            .setMessage(String.format(errorText, excel.getDeptName(), "学段不对")));
                }
                //学校性质
                if (NatureCode.TOWN.getDescription().equals(excel.getNatureStr()) || NatureCode.CITY.getDescription().equals(excel.getNatureStr()) || NatureCode.NEW_CITY.getDescription().equals(excel.getNatureStr())|| NatureCode.INDUSTRIAL_CITY.getDescription().equals(excel.getNatureStr())) {

                } else {
                    errorMessage.add(new ExcelBatchImportResponse().setRowIndex(total + 1)
                            .setMessage(String.format(errorText, excel.getDeptName(), "学校性质不对")));
                }
                //学校类型
                if (SchoolTypeCode.PUBLIC.getDescription().equals(excel.getTypeStr()) || SchoolTypeCode.PRIVATE.getDescription().equals(excel.getTypeStr())) {

                } else {
                    errorMessage.add(new ExcelBatchImportResponse().setRowIndex(total + 1)
                            .setMessage(String.format(errorText, excel.getDeptName(), "学校类型不对")));
                }
                total++;
            }

            pageNumber++;
        } while (total < excelDataList.size());
        return errorMessage;
    }

    @SneakyThrows
    @Override
    public String downTemplate(HttpServletResponse response) {
        List<String> titles = new ArrayList<>();
//        titles.add("所属区县");
        titles.add("学校名称");
        titles.add("学校性质");
        titles.add("学校类型");
        titles.add("学段");
        titles.add("联系人");
        titles.add("联系电话");
        titles.add("招生计划数");
        titles.add("地址");
        ExcelWriter writer = ExcelUtil.getWriter();
        writer.writeHeadRow(titles);
        //列自适应宽度
        writer.autoSizeColumnAll();
        int columnCount = writer.getColumnCount();
        setSizeColumn(writer.getSheet(), columnCount - 1);
        response.setContentType("application/octet-stream");
        response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode("学校导入模板") + ".xls");
        response.flushBuffer();
        OutputStream output = response.getOutputStream();
        writer.flush(output, true);
        // 关闭writer，释放内存
        writer.close();
        return null;
    }
    @SneakyThrows
    @Override
    public String downTemplates(HttpServletResponse response) {
        List<String> titles = new ArrayList<>();
//        titles.add("所属区县");
        titles.add("学校名称");
        titles.add("学校性质");
        titles.add("学校类型");
        titles.add("学段");
        titles.add("联系人");
        titles.add("联系电话");
        titles.add("招生计划数");
        titles.add("地址");
        titles.add("学校标识");
        ExcelWriter writer = ExcelUtil.getWriter();
        writer.writeHeadRow(titles);
        //列自适应宽度
        writer.autoSizeColumnAll();
        int columnCount = writer.getColumnCount();
        setSizeColumn(writer.getSheet(), columnCount - 1);
        response.setContentType("application/octet-stream");
        response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode("学校导入模板") + ".xls");
        response.flushBuffer();
        OutputStream output = response.getOutputStream();
        writer.flush(output, true);
        // 关闭writer，释放内存
        writer.close();
        return null;
    }


    @SneakyThrows
    @Override
    public String downSchoolRelacionTemplate(HttpServletResponse response) {
        List<String> titles = new ArrayList<>();
//        titles.add("所属区县");
        titles.add("初中学校");
        titles.add("对口小学");
        ExcelWriter writer = ExcelUtil.getWriter();
        writer.writeHeadRow(titles);
        //列自适应宽度
        writer.autoSizeColumnAll();
        int columnCount = writer.getColumnCount();
        setSizeColumn(writer.getSheet(), columnCount - 1);
        response.setContentType("application/octet-stream");
        response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode("学校关系导入模板") + ".xls");
        response.flushBuffer();
        OutputStream output = response.getOutputStream();
        writer.flush(output, true);
        // 关闭writer，释放内存
        writer.close();
        return null;
    }



    /**
     * 报名端--获取学校列表
     *
     * @param search
     * @return
     */
    @Override
    public PageInfo<DeptInfo> getSchoolPageData(DeptSearch search) {
        Map<Long, String> rangMap = new HashMap<>();
        AccountInfo info = (AccountInfo) redisTemplate.opsForValue().get(RedisKeyConstant.CURRENT_USER_INFO + StpUtil.getTokenValue());
        search.setLevel(3);
        search.setStatus("1");
        Dept dept = baseMapper.selectOne(new LambdaQueryWrapper<Dept>().eq(Dept::getDeptCode, search.getDeptCode()));
        if (Objects.nonNull(dept)) {
            search.setParentId(dept.getId());
        } else {
            throw new SecurityException("请先选择区县");
        }
        if (Objects.isNull(search.getParentId())) {
            throw new SecurityException("请先选择区县");
        }
        if (Objects.isNull(search.getSetUpId())) {
            throw new SecurityException("请选择报名入口！");
        }
        String setUpType = "";
        // 特殊处理民办小学和民办初中列表
        if (search.getSetUpId() == 1006 || search.getSetUpId() == 1007) {
            SchoolRangSearch schoolRangSearch = new SchoolRangSearch();
            schoolRangSearch.setCountyId(search.getParentId());
            List<SchoolRangInfo> rangs = schoolRangMapper.getInfoListDataOne(schoolRangSearch);
            rangMap = rangs != null && rangs.size() > 0 ?
                    rangs.stream().collect(Collectors.toMap(SchoolRangInfo::getSchoolId, SchoolRangInfo::getRangeName)) : new HashMap<>();
            IPage<Dept> page = new Page<Dept>(search.getPageNumber(), search.getPageSize());
            LambdaQueryWrapper<Dept> queryWrapper = new LambdaQueryWrapper<Dept>();
            queryWrapper.likeRight(StrUtil.isNotBlank(search.getAncestors()), Dept::getAncestors, search.getAncestors())
                    .eq(Dept::getType, "2") // 设置为民办
                    .eq(Dept::getPeriod, search.getSetUpId() == 1006 ? "2" : "3") // 1006为小学，1007为初中
                    .eq(StrUtil.isNotBlank(search.getIsFive()), Dept::getIsFive, search.getIsFive())
                    .eq(StrUtil.isNotBlank(search.getNature()), Dept::getNature, search.getNature())
                    .eq(search.getParentId() != null, Dept::getParentId, search.getParentId())
                    .eq(search.getLevel() != null, Dept::getLevel, search.getLevel())
                    .eq(StrUtil.isNotBlank(search.getStatus()), Dept::getStatus, search.getStatus())
                    .orderByAsc(Dept::getSort)
                    .orderByAsc(Dept::getId);
            page = super.page(page, queryWrapper);
            PageInfo<DeptInfo> result = PageInfo.fromMybatisPage(page, DeptInfo.class);
            if (!Objects.isNull(result.getRecords()) && RoleConstant.PARENT_CODE.equals(info.getRoleCode())) {
                for (DeptInfo deptInfo : result.getRecords()) {
                    deptInfo.setArea(rangMap.containsKey(deptInfo.getId()) ? rangMap.get(deptInfo.getId()) : null);
                }
            }
            return result;
        }
        switch (search.getSetUpId()) {
            //房户一致
            case 17:
            case 25:
            case 44:
            case 52:
            case 73:
            case 81:
            case 100:
            case 108:
                setUpType = "A";
                break;
            //户口
            case 18:
            case 26:
            case 45:
            case 53:
            case 74:
            case 82:
            case 101:
            case 109:
                setUpType = "B";
                break;
            case 19:
            case 27:
            case 46:
            case 54:
            case 75:
            case 83:
            case 102:
            case 110:
                setUpType = "C";
                break;
            case 20:
            case 28:
            case 47:
            case 55:
            case 76:
            case 84:
            case 103:
            case 111:
                setUpType = "D";
                break;
            case 21:
            case 29:
            case 48:
            case 56:
            case 77:
            case 85:
            case 104:
            case 112:
            case 401:
            case 402:
            case 403:
            case 404:
                setUpType = "E";
                break;
            case 22:
            case 30:
            case 49:
            case 57:
            case 78:
            case 86:
            case 105:
            case 113:
                setUpType = "F";
                break;
            case 23:
            case 31:
            case 50:
            case 58:
            case 79:
            case 87:
            case 106:
            case 114:
                setUpType = "G";
                break;
            case 24:
            case 32:
            case 51:
            case 59:
            case 80:
            case 88:
            case 107:
            case 115:
                setUpType = "H";
                break;
            default:
                throw new ServiceException(RestCode.DATA_ERROR,"选择报名入口错误！SetUpId: " + search.getSetUpId());
        }
        SchoolRangSearch schoolRangSearch = new SchoolRangSearch();
        schoolRangSearch.setCountyId(search.getParentId());
        List<SchoolRangInfo> rangs = schoolRangMapper.getInfoListDataOne(schoolRangSearch);
        rangMap = rangs != null && rangs.size() > 0 ?
                rangs.stream().collect(Collectors.toMap(SchoolRangInfo::getSchoolId, SchoolRangInfo::getRangeName)) : new HashMap<>();
        IPage<Dept> page = new Page<Dept>(search.getPageNumber(), search.getPageSize());
        LambdaQueryWrapper<Dept> queryWrapper = new LambdaQueryWrapper<Dept>();
        queryWrapper.likeRight(StrUtil.isNotBlank(search.getAncestors()), Dept::getAncestors, search.getAncestors())
                .eq(StrUtil.isNotBlank(search.getType()), Dept::getType, search.getType())
                .eq(StrUtil.isNotBlank(search.getPeriod()), Dept::getPeriod, search.getPeriod())
                .eq(StrUtil.isNotBlank(search.getIsFive()), Dept::getIsFive, search.getIsFive())
                .eq(StrUtil.isNotBlank(search.getNature()), Dept::getNature, search.getNature())
                .eq(search.getParentId() != null, Dept::getParentId, search.getParentId())
                .eq(search.getLevel() != null, Dept::getLevel, search.getLevel())
                .eq(StrUtil.isNotBlank(search.getStatus()), Dept::getStatus, search.getStatus())
                .like(Dept::getEnrollType,setUpType)
                .orderByAsc(Dept::getSort)
                .orderByAsc(Dept::getId);
        page = super.page(page, queryWrapper);
        PageInfo<DeptInfo> result = PageInfo.fromMybatisPage(page, DeptInfo.class);
        if (!Objects.isNull(result.getRecords()) && RoleConstant.PARENT_CODE.equals(info.getRoleCode())) {
            for (DeptInfo deptInfo : result.getRecords()) {
                deptInfo.setArea(rangMap.containsKey(deptInfo.getId()) ? rangMap.get(deptInfo.getId()) : null);
            }
        }
        return result;
    }

    @Override
    public List<ExcelBatchImportResponse> importSchoolRelation(MultipartFile file) {

        AccountInfo info = (AccountInfo) redisTemplate.opsForValue().get(RedisKeyConstant.CURRENT_USER_INFO + StpUtil.getTokenValue());
        if (!RoleConstant.COUNTY_ADMIN_CODE.equals(info.getRoleCode())) {
            throw new SecurityException("无操作权限");
        }
        List<ExcelBatchImportResponse> errorMessage = new ArrayList<>();
        if (Objects.nonNull(file)) {
            try {
                ExcelHelper<SchoolRelationExcel> excelHelper = ExcelHelper.create(SchoolRelationExcel.class);
                List<SchoolRelationExcel> excelDataList = excelHelper.readData(file.getInputStream());
                if (excelDataList.size() > 0) {
//                    errorMessage = this.checkExcelData(excelDataList);
//                    if (errorMessage.size() > 0) {
//                        return errorMessage;
//                    }
//                    excelDataList = manageData(excelDataList);
                    int pageNumber = 0, pageSize = 500, total = 0;
                    do {
                        try {
                            List<PrimaryJunior> scoreList = new ArrayList<>();
                            List<SchoolRelationExcel> dataList = excelDataList.stream().skip((long) pageNumber * pageSize).limit(pageSize).collect(Collectors.toList());
                            for (SchoolRelationExcel excel : dataList) {
                                PrimaryJunior primaryJunior = new PrimaryJunior();
                                primaryJunior.setId(idGenerator.getNextId());
                                //获取小学Id.
                                //获取初中ID
                                LambdaQueryWrapper<Dept> queryWrapper = new LambdaQueryWrapper<Dept>();
                                queryWrapper.eq(Dept::getDeptName,excel.getPrimarySchoolName())
                                        .eq(Dept::getParentId, info.getDeptId());
                                Dept primary = super.getOne(queryWrapper);
                                if(null == primary){
                                    throw new SecurityException("学校"+excel.getPrimarySchoolName()+"不存在");
                                }
                                queryWrapper = new LambdaQueryWrapper<Dept>();
                                queryWrapper.eq(Dept::getDeptName,excel.getJuniorSchoolName())
                                        .eq(Dept::getParentId, info.getDeptId());
                                Dept Junior = super.getOne(queryWrapper);
                                if(null == Junior){
                                    throw new SecurityException("学校"+excel.getJuniorSchoolName()+"<不存在");
                                }
                                primaryJunior.setJuniorId(Junior.getId());
                                primaryJunior.setPrimaryId(primary.getId());
                                scoreList.add(primaryJunior);
                                total++;
                            }
                            //保存
                            for (PrimaryJunior primaryJunior : scoreList) {
                                primaryJuniorMapper.insert(primaryJunior);
                            }
                            scoreList.clear();
                            pageNumber++;
                        } catch (Exception e) {
                            errorMessage.add(new ExcelBatchImportResponse().setRowIndex(total).setMessage(e.getMessage()));
                            throw e;
                        }
                    } while (total < excelDataList.size());
                } else {
                    errorMessage.add(new ExcelBatchImportResponse().setMessage("未检测到数据").setRowIndex(1));
                }
            } catch (Exception e) {
                throw new SecurityException(e.getMessage());
            }
        }
        return errorMessage;
    }

    /**
     * 获取小学对口初中详情
     * @param key
     * @return
     */
    @Override
    public DeptInfo primaryJuniorDetail(Long key) {
        // 先查询小学信息
        Dept primarySchool = baseMapper.selectById(key);
        if (null == primarySchool) {
            throw new SecurityException("未找到小学信息");
        }

        LambdaQueryWrapper<PrimaryJunior> queryWrapper = new LambdaQueryWrapper<PrimaryJunior>();
        queryWrapper.eq(PrimaryJunior::getPrimaryId,key);
        PrimaryJunior primaryJunior = primaryJuniorMapper.selectOne(queryWrapper);
        if(null == primaryJunior){
            throw new SecurityException("没有对口学校");
        }
        //对口学校
        Long juniorId = primaryJunior.getJuniorId();
        DeptInfo deptDetail = this.getDeptDetail(juniorId);
        if(null == deptDetail){
            throw new SecurityException("没有对口学校");
        }
        deptDetail.setParentName(primarySchool.getDeptName());
        return deptDetail;
    }

    public static void setSizeColumn(Sheet sheet, int size) {
        for (int columnNum = 0; columnNum <= size; columnNum++) {
            int columnWidth = sheet.getColumnWidth(columnNum) / 256;
            for (int rowNum = 0; rowNum <= sheet.getLastRowNum(); rowNum++) {
                Row currentRow;
                //当前行未被使用过
                if (sheet.getRow(rowNum) == null) {
                    currentRow = sheet.createRow(rowNum);
                } else {
                    currentRow = sheet.getRow(rowNum);
                }

                if (currentRow.getCell(columnNum) != null) {
                    Cell currentCell = currentRow.getCell(columnNum);
                    if (currentCell.getCellType() == CellType.STRING) {
                        int length = currentCell.getStringCellValue().getBytes().length;
                        if (columnWidth < length) {
                            columnWidth = length;
                        }
                    }
                }
            }
            sheet.setColumnWidth(columnNum, columnWidth * 256);
        }
    }
}
