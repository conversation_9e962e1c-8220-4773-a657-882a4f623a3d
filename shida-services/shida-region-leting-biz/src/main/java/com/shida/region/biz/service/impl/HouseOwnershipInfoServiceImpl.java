package com.shida.region.biz.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.listener.PageReadListener;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.shida.enums.RestCode;
import com.shida.exceptions.ServiceException;
import com.shida.pojo.vo.PageInfo;
import com.shida.properties.DeptProperties;
import com.shida.region.biz.entity.*;
import com.shida.region.biz.enums.*;
import com.shida.region.biz.frame.Constant;
import com.shida.region.biz.mapper.HouseOwnershipInfoMapper;
import com.shida.region.biz.mapper.StudentBaseInfoMapper;
import com.shida.region.biz.mapper.StudentBaseMapper;
import com.shida.region.biz.pojo.excel.HousePropertyExcel;
import com.shida.region.biz.pojo.excel.PublicSecurityRegistrationExcel;
import com.shida.region.biz.pojo.excel.PublicSecurityResidenceExcel;
import com.shida.region.biz.pojo.from.SecurityAndHouseNoPassForm;
import com.shida.region.biz.pojo.search.PublicSecurityAndHouseSearch;
import com.shida.region.biz.pojo.vo.CxSecurityAndHouseVo;
import com.shida.region.biz.pojo.vo.PublicSecurityAndHousePropertyVo;
import com.shida.region.biz.pojo.vo.StudentVo;
import com.shida.region.biz.service.*;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jodd.util.StringUtil;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>
 * 学生报名房屋所有权证信息表 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2024-04-30
 */
@Service
public class HouseOwnershipInfoServiceImpl extends ServiceImpl<HouseOwnershipInfoMapper, HouseOwnershipInfo> implements IHouseOwnershipInfoService {

    @Resource
    private StudentBaseInfoMapper studentBaseInfoMapper;
    @Resource
    private StudentBaseMapper studentBaseMapper;
    @Resource
    private DeptProperties deptProperties;
    @Resource
    private IResidenceInfoService residenceInfoService;
    @Resource
    private IHomesteadLicenseInfoService homesteadLicenseInfoService;
    @Resource
    private INaturalVillageInfoService naturalVillageInfoService;
    @Resource
    private IMortgageContractInfoService mortgageContractInfoService;
    @Resource
    private IRaiseFundsInfoService raiseFundsInfoService;
    @Resource
    private ISmallPropertyInfoService smallPropertyInfoService;
    @Resource
    private ILeaseHouseInfoService leaseHouseInfoService;
    @Resource
    private IEstateInfoService estateInfoService;
    @Resource
    private ILandUseInfoService landUseInfoService;
    @Resource
    private IHousePurchaseContractInfoService housePurchaseContractInfoService;

    /**
     * 获取公共安全和房屋属性的分页信息
     *
     * @param search 公共安全和房屋搜索条件对象，用于指定查询条件
     * @return PageInfo<PublicSecurityAndHousePropertyVo> 分页信息对象，包含查询到的公共安全和房屋属性的结果集
     */
    @Override
    public PageInfo<PublicSecurityAndHousePropertyVo> getPublicSecurityAndHousePropertyPage(PublicSecurityAndHouseSearch search) {
        if (ObjectUtil.isNotEmpty(search.getCreatedTime())
                && ObjectUtil.isNotEmpty(search.getEndTime())
                && search.getEndTime().isBefore(search.getCreatedTime())) {
            throw new ServiceException(RestCode.DATA_ERROR, "结束时间不能小于开始时间");
        }
        IPage<PublicSecurityAndHousePropertyVo> page = new Page<>(search.getPageNumber(), search.getPageSize());
        List<Integer> setupIdList = Arrays.stream(studentBaseInfoMapper.selectSetUpIdsName(search.getPublicSecurityReviewType()).split(","))
                .map(Integer::parseInt)
                .collect(Collectors.toList());
        page = studentBaseMapper.getPublicSecurityAndHousePropertyPage(page, search,setupIdList);


        page.getRecords().forEach(item -> {
            item.setEnrollStageText(PeriodEnum.getMessageByCode(item.getEnrollStage().toString()));
            //公安/房产类型  0：公安 1：房管
            if (search.getType().equals(PublicSecurityAndHousePropertyTypeEnum.PUBLICSECURITY.getCode())) {
                getPublicSecurityInfo(item);
            } else if (search.getType().equals(PublicSecurityAndHousePropertyTypeEnum.HOUSEPROPERTY.getCode())) {
                getHousePropertyVoInfo(item);
            }
        });
        return PageInfo.fromMybatisPage(page);
    }

    @Override
    public PageInfo<PublicSecurityAndHousePropertyVo> getHukouAndJuzhuPage(PublicSecurityAndHouseSearch search) {
        if (ObjectUtil.isNotEmpty(search.getCreatedTime())
                && ObjectUtil.isNotEmpty(search.getEndTime())
                && search.getEndTime().isBefore(search.getCreatedTime())) {
            throw new ServiceException(RestCode.DATA_ERROR, "结束时间不能小于开始时间");
        }
        IPage<PublicSecurityAndHousePropertyVo> page = new Page<>(search.getPageNumber(), search.getPageSize());
        if ( 1 == search.getPublicSecurityReviewType().intValue()){
            page = studentBaseMapper.getHukouPage(page, search);
        }else if (2 == search.getPublicSecurityReviewType().intValue()) {
            page = studentBaseMapper.getJuzhuPage(page, search);
        }


        page.getRecords().forEach(item -> {
            item.setEnrollStageText(PeriodEnum.getMessageByCode(item.getEnrollStage().toString()));
            //公安/房产类型  0：公安 1：房管
            if (search.getType().equals(PublicSecurityAndHousePropertyTypeEnum.PUBLICSECURITY.getCode())) {
                getPublicSecurityInfo(item);
            }
        });
        return PageInfo.fromMybatisPage(page);
    }




    /**
     * 导出房屋财产信息。
     * @param search 公共安全和房屋搜索对象，包含需要导出的信息筛选条件。
     * @param response HTTP响应对象，用于将信息导出到客户端。
     * @throws IOException 如果导出过程中发生IO异常。
     */
    @Override
    public void exportHouseProperty(PublicSecurityAndHouseSearch search, HttpServletResponse response) throws IOException {
        Workbook wb = new XSSFWorkbook();

        Sheet sheet = wb.createSheet();
        sheet.protectSheet("password");

        CellStyle style = wb.createCellStyle();
        CellStyle styleLocked = wb.createCellStyle();
        //列宽
        sheet.setDefaultColumnWidth(20);
        // 左右居中
        style.setAlignment(HorizontalAlignment.CENTER);
        // 上下居中
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        Row row = sheet.createRow(0);
        Cell cell = row.createCell(0);
        cell.setCellValue("报名ID");
        cell.setCellStyle(style);
        cell = row.createCell(1);
        cell.setCellValue("学生姓名");
        cell.setCellStyle(style);
        cell = row.createCell(2);
        cell.setCellValue("身份证号");
        cell.setCellStyle(style);
        cell = row.createCell(3);
        cell.setCellValue("类别");
        cell.setCellStyle(style);
        cell = row.createCell(4);
        cell.setCellValue("证件编号");
        cell.setCellStyle(style);
        cell = row.createCell(5);
        cell.setCellValue("房主姓名");
        cell.setCellStyle(style);
        cell = row.createCell(6);
        cell.setCellValue("与该生关系");
        cell.setCellStyle(style);
        cell = row.createCell(7);
        cell.setCellValue("报名学校名称");
        cell.setCellStyle(style);

        cell = row.createCell(8);
        cell.setCellStyle(style);
        cell.setCellValue("学段");

        cell = row.createCell(9);
        cell.setCellValue("房管部门审核");
        cell.setCellStyle(style);


        List<PublicSecurityAndHousePropertyVo> data = getPublicSecurityExcelData(search);

//            List<HousePropertyExcel> data = BeanUtil.copyToList(list, HousePropertyExcel.class);
        if (data.size() != 0) {
            for (int i = 0; i < data.size(); i++) {
                row = sheet.createRow(i + 1);
                cell = row.createCell(0);
                cell.setCellValue(data.get(i).getEnrollId());
                cell.setCellStyle(style);
                cell = row.createCell(1);
                cell.setCellValue(data.get(i).getStudentName());
                cell.setCellStyle(style);
                cell = row.createCell(2);
                cell.setCellValue(data.get(i).getStudentIdCardNumber());
                cell.setCellStyle(style);
                cell = row.createCell(3);
                cell.setCellValue(data.get(i).getType());
                cell.setCellStyle(style);
                cell = row.createCell(4);
                cell.setCellValue(data.get(i).getCertificateCode());
                cell.setCellStyle(style);
                cell = row.createCell(5);
                cell.setCellValue(data.get(i).getResidencePermitName());
                cell.setCellStyle(style);
                cell = row.createCell(6);
                cell.setCellValue(data.get(i).getRelationOfStudentText());
                cell.setCellStyle(style);
                cell = row.createCell(7);
                cell.setCellValue(data.get(i).getEnrollSchoolName());
                cell.setCellStyle(style);

                cell = row.createCell(8);
                cell.setCellValue(data.get(i).getEnrollStageText());
                cell.setCellStyle(style);

                cell = row.createCell(9);
                cell.setCellValue(data.get(i).getEstateReviewStatus());
                //只有状态 可以修改数据 ，其他只读
                styleLocked.setLocked(false);
                cell.setCellStyle(styleLocked);

            }
        }

        response.setContentType("application/octet-stream");
        response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode("房管部门信息审核") + DateUtil.current() + ".xlsx");
        response.flushBuffer();
        OutputStream out = response.getOutputStream();
        wb.write(out);
        wb.close();
        out.close();
    }


    /**
     * 导出警方信息。
     * @param search 公共安全和房屋搜索对象，包含需要导出的信息筛选条件。
     * @param response HTTP响应对象，用于将信息导出到客户端。
     * @throws IOException 如果导出过程中发生IO异常。
     */
    @Override
    public void exportPoliceInformation(PublicSecurityAndHouseSearch search, HttpServletResponse response) throws IOException {
            otherPoliceExport(search, response);
    }


    /**
     * 导入警方信息。
     * @param file 包含警方信息的多部分文件。
     */
    @Override
    @Transactional(rollbackFor = SecurityException.class)
    public void importPoliceInformation(MultipartFile file, Integer publicSecurityReviewType) {
        try {
            Class head;
            switch (publicSecurityReviewType) {
                case 1:
                    head = PublicSecurityRegistrationExcel.class;
                    PageReadListener<PublicSecurityRegistrationExcel> readListener1 = new PageReadListener<>(dataList -> {
                        dataList.forEach(item -> {
                            if (StringUtils.isBlank(item.getEnrollId())) {
                                throw new ServiceException(RestCode.DATA_ERROR.code(), "报名ID不能为空");
                            }

                            if (StringUtil.isNotEmpty(item.getPublicSecurityReviewStatus())) {
                                //根据状态文本获取状态数字
                                String codeByMessage = AuditEnum.getCodeByMessage(item.getPublicSecurityReviewStatus());
                                if (StrUtil.isBlank(codeByMessage)) {
                                    throw new ServiceException(RestCode.DATA_ERROR, "审核结果只能是“通过”，“不通过”，“待审核”");
                                }
                                item.setPublicSecurityReviewStatus(codeByMessage);
                                //通过身份证查找对应学生
                                StudentVo student = studentBaseMapper.getStudentInfoByEnrollId(item.getEnrollId());
                                Optional.ofNullable(ObjectUtil.isEmpty(student) ? null : student)
                                        .orElseThrow(() -> new ServiceException(RestCode.DATA_ERROR.code(), "该生信息无法找到"));
                                //更新 公安/房产 审核状态
                                studentBaseMapper.update(null, new LambdaUpdateWrapper<StudentBase>()
                                        .eq(StudentBase::getId, student.getStudentId())
                                        .eq(StudentBase::getDeleteFlag, DeleteFlagEnum.TRUE.getCode())
                                        .set(StudentBase::getPublicSecurityReviewStatus, item.getPublicSecurityReviewStatus())
                                        .set(StudentBase::getPublicSecurityRegistrationReviewStatus, item.getPublicSecurityReviewStatus()));
                            } else {
                                throw new ServiceException(RestCode.DATA_ERROR, "审核结果不能为空！");
                            }
                        });
                    });
                    EasyExcel.read(file.getInputStream(), head, readListener1).sheet().headRowNumber(2).doRead();
                    break;
                case 2:
                    head = PublicSecurityResidenceExcel.class;
                    PageReadListener<PublicSecurityResidenceExcel> readListener2 = new PageReadListener<>(dataList -> {
                        dataList.forEach(item -> {
                            if (StringUtils.isBlank(item.getEnrollId())) {
                                throw new ServiceException(RestCode.DATA_ERROR.code(), "报名ID不能为空");
                            }

                            if (StringUtil.isNotEmpty(item.getPublicSecurityReviewStatus())) {
                                //根据状态文本获取状态数字
                                String codeByMessage = AuditEnum.getCodeByMessage(item.getPublicSecurityReviewStatus());
                                if (StrUtil.isBlank(codeByMessage)) {
                                    throw new ServiceException(RestCode.DATA_ERROR, "审核结果只能是“通过”，“不通过”，“待审核”");
                                }
                                item.setPublicSecurityReviewStatus(codeByMessage);
                                //通过身份证查找对应学生
                                StudentVo student = studentBaseMapper.getStudentInfoByEnrollId(item.getEnrollId());
                                Optional.ofNullable(ObjectUtil.isEmpty(student) ? null : student)
                                        .orElseThrow(() -> new ServiceException(RestCode.DATA_ERROR.code(), "该生信息无法找到"));
                                //更新 公安/房产 审核状态
                                studentBaseMapper.update(null, new LambdaUpdateWrapper<StudentBase>()
                                        .eq(StudentBase::getId, student.getStudentId())
                                        .eq(StudentBase::getDeleteFlag, DeleteFlagEnum.TRUE.getCode())
                                        .set(StudentBase::getPublicSecurityReviewStatus, item.getPublicSecurityReviewStatus())
                                        .set(StudentBase::getPublicSecurityResidenceReviewStatus, item.getPublicSecurityReviewStatus()));
                            } else {
                                throw new ServiceException(RestCode.DATA_ERROR, "审核结果不能为空！");
                            }
                        });
                    });
                    EasyExcel.read(file.getInputStream(), head, readListener2).sheet().headRowNumber(2).doRead();
                    break;
                default:
                    throw new ServiceException(RestCode.DATA_ERROR, "上传参数错误");
            }
//            EasyExcel.read(file.getInputStream(), head, readListener).sheet().doRead();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 通过公安和房屋审核
     * @param form 搜索条件
     */
    @Override
    public void passSecurityAndHouseAudit(SecurityAndHouseNoPassForm form) {
        if (form.getType().equals(PublicSecurityAndHousePropertyTypeEnum.PUBLICSECURITY.code())) {
            studentBaseMapper.update(new LambdaUpdateWrapper<StudentBase>()
                    .eq(StudentBase::getId, form.getStudentId()).set(StudentBase::getPublicSecurityReviewStatus, 2));
        } else {
            studentBaseMapper.update(new LambdaUpdateWrapper<StudentBase>()
                    .eq(StudentBase::getId, form.getStudentId()).set(StudentBase::getEstateReviewStatus, 2));
        }
    }

    /**
     * 不通过公安和房屋审核
     * @param form 包含不通过审核的具体信息的表单
     */
    @Override
    public void notPassSecurityAndHouseAudit(SecurityAndHouseNoPassForm form) {
        if (form.getType().equals(PublicSecurityAndHousePropertyTypeEnum.PUBLICSECURITY.code())) {
            studentBaseMapper.update(new LambdaUpdateWrapper<StudentBase>()
                    .eq(StudentBase::getId, form.getStudentId())
                    .set(StudentBase::getPublicSecurityReviewStatus, 3)
                    .set(StudentBase::getPublicSecurityReviewReason, form.getCause())
            );
        } else {
            studentBaseMapper.update(new LambdaUpdateWrapper<StudentBase>()
                    .eq(StudentBase::getId, form.getStudentId())
                    .set(StudentBase::getEstateReviewStatus, 3)
                    .set(StudentBase::getEstateReviewReason, form.getCause())
            );
        }
    }

    /**
     * 导入房屋财产信息。
     * @param file 包含房屋财产信息的多部分文件。
     */
    @Override
    @Transactional(rollbackFor = SecurityException.class)
    public void importHouseProperty(MultipartFile file) {
        try {
            EasyExcel.read(file.getInputStream(), HousePropertyExcel.class, new PageReadListener<HousePropertyExcel>(dataList -> {
                dataList.forEach(item -> {
                    if (StringUtils.isBlank(item.getEnrollId())) {
                        throw new ServiceException(RestCode.DATA_ERROR.code(), "报名ID不能为空");
                    }

                    if (StringUtil.isNotEmpty(item.getEstateReviewStatus())) {
                        //根据状态文本获取状态数字
                        item.setEstateReviewStatus(AuditEnum.getCodeByMessage(item.getEstateReviewStatus()));
                        //通过身份证查找对应学生
                        StudentVo student = studentBaseMapper.getStudentInfoByEnrollId(item.getEnrollId());
                        Optional.ofNullable(ObjectUtil.isEmpty(student) ? null : student)
                                .orElseThrow(() -> new ServiceException(RestCode.DATA_ERROR.code(), "该生信息无法找到"));
                        //更新 房产 审核状态
                        studentBaseMapper.update(null, new LambdaUpdateWrapper<StudentBase>()
                                .eq(StudentBase::getId, student.getStudentId())
                                .eq(StudentBase::getDeleteFlag, DeleteFlagEnum.TRUE.getCode())
                                .set(StudentBase::getEstateReviewStatus, item.getEstateReviewStatus()));
                    }
                });
            })).sheet().headRowNumber(2).doRead();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    private void otherPoliceExport(PublicSecurityAndHouseSearch search, HttpServletResponse response) throws IOException {
        Workbook wb = new XSSFWorkbook();

        CellStyle style = wb.createCellStyle();
        CellStyle styleLocked = wb.createCellStyle();

        Sheet sheet = wb.createSheet();
        sheet.protectSheet("password");
        //列宽
        sheet.setDefaultColumnWidth(20);

        style.setAlignment(HorizontalAlignment.CENTER);// 左右居中
        style.setVerticalAlignment(VerticalAlignment.CENTER);// 上下居中


        Row row = sheet.createRow(0);
        Cell cell = row.createCell(0);
        cell.setCellValue("报名ID");
        cell.setCellStyle(style);
        cell = row.createCell(1);
        cell.setCellValue("学生姓名");
        cell.setCellStyle(style);
        cell = row.createCell(2);
        cell.setCellStyle(style);
        cell.setCellValue("身份证号");
        cell = row.createCell(3);
        cell.setCellStyle(style);
        cell.setCellValue("类别");
        cell = row.createCell(4);
        cell.setCellStyle(style);
        cell.setCellValue("户号");
        cell = row.createCell(5);
        cell.setCellStyle(style);
        cell.setCellValue("居住证编号");
        cell = row.createCell(6);
        cell.setCellStyle(style);
        cell.setCellValue("户主");
        cell = row.createCell(7);
        cell.setCellStyle(style);
        cell.setCellValue("与该生关系");
        cell = row.createCell(8);
        cell.setCellStyle(style);
        cell.setCellValue("报名学校名称");
        cell = row.createCell(9);
        cell.setCellStyle(style);
        cell.setCellValue("学段");
        cell = row.createCell(10);
        cell.setCellStyle(style);
        if (ObjectUtil.equal(search.getPublicSecurityReviewType(), 1)) {
            cell.setCellValue("公安户口审核");
        } else if (ObjectUtil.equal(search.getPublicSecurityReviewType(), 2)) {
            cell.setCellValue("公安居住证审核");
        } else {
            throw new ServiceException(RestCode.DATA_ERROR, "公安审核类型参数错误");
        }


        List<PublicSecurityAndHousePropertyVo> data = getPublicSecurityExcelData(search);
        if (data.size() > 0) {
//            List<PublicSecurityExcel> data = BeanUtil.copyToList(list, PublicSecurityExcel.class);
            for (int i = 0; i < data.size(); i++) {
                row = sheet.createRow(i + 1);
                cell = row.createCell(0);
                cell.setCellValue(data.get(i).getEnrollId());
                cell.setCellStyle(style);
                cell = row.createCell(1);
                cell.setCellValue(data.get(i).getStudentName());
                cell.setCellStyle(style);
                cell = row.createCell(2);
                cell.setCellValue(data.get(i).getStudentIdCardNumber());
                cell.setCellStyle(style);
                cell = row.createCell(3);
                cell.setCellValue(data.get(i).getType());
                cell.setCellStyle(style);
                cell = row.createCell(4);
                cell.setCellValue(data.get(i).getAccountNumber());
                cell.setCellStyle(style);
                cell = row.createCell(5);
                cell.setCellValue(data.get(i).getCertificateCode());
                cell.setCellStyle(style);
                cell = row.createCell(6);
                cell.setCellValue(data.get(i).getHouseholdName());
                cell.setCellStyle(style);
                cell = row.createCell(7);
                cell.setCellValue(data.get(i).getRelationOfStudentText());
                cell.setCellStyle(style);
                cell = row.createCell(8);
                cell.setCellValue(data.get(i).getEnrollSchoolName());
                cell.setCellStyle(style);

                cell = row.createCell(9);
                cell.setCellValue(data.get(i).getEnrollStageText());
                cell.setCellStyle(style);

                cell = row.createCell(10);
                cell.setCellValue(data.get(i).getPublicSecurityReviewStatus());
                //只有状态 可以修改数据 ，其他只读
                styleLocked.setLocked(false);
                cell.setCellStyle(styleLocked);
            }
        }
        response.setContentType("application/octet-stream");
        response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode("公安信息核对") + DateUtil.current() + ".xlsx");
        response.flushBuffer();
        OutputStream out = response.getOutputStream();
        wb.write(out);
        wb.close();
        out.close();
    }

    private List<PublicSecurityAndHousePropertyVo> getPublicSecurityExcelData(PublicSecurityAndHouseSearch search) {
        if (ObjectUtil.isNotEmpty(search.getCreatedTime())
                && ObjectUtil.isNotEmpty(search.getEndTime())
                && search.getEndTime().isBefore(search.getCreatedTime())) {
            throw new ServiceException(RestCode.DATA_ERROR.code(), "结束时间不能小于开始时间");
        }
        List<PublicSecurityAndHousePropertyVo> list = studentBaseMapper.getPublicSecurityList(search);
        if (list.size() == 0) {
            return list;
        }
        list.forEach(item -> {
            item.setEnrollStageText(PeriodEnum.getMessageByCode(item.getEnrollStage().toString()));
            //与学生关系
            if (ObjectUtil.isNotEmpty(item.getRelationOfStudent())) {
                String relationOfStudentText = RelationOfStudent.getMessageByCode(item.getRelationOfStudent());
                item.setRelationOfStudentText(relationOfStudentText);
            }
            //公安/房产类型  0：公安 1：房管
            if (search.getType().equals(PublicSecurityAndHousePropertyTypeEnum.PUBLICSECURITY.getCode())) {
                getPublicSecurityInfo(item);
            } else if (search.getType().equals(PublicSecurityAndHousePropertyTypeEnum.HOUSEPROPERTY.getCode())) {
                getHousePropertyVoInfo(item);
            }
        });
        return list;
    }

    /**
     * 公安字段赋值
     *
     * @param item
     */
    private void getPublicSecurityInfo(PublicSecurityAndHousePropertyVo item) {
        //公安审核状态
        if (ObjectUtil.isNotEmpty(item.getPublicSecurityReviewStatus())) {
            item.setPublicSecurityReviewStatus(AuditEnum.getMessageByCode(item.getPublicSecurityReviewStatus()));
        }
        item.setType(deptProperties.getName() + "-" + item.getType());
        //赋值居住证字段
//        ResidenceInfo residenceInfo = residenceInfoService.getOne(new LambdaUpdateWrapper<ResidenceInfo>()
//                .eq(ResidenceInfo::getStudentId, item.getStudentBaseId()));
//        if (ObjectUtil.isNotEmpty(residenceInfo)) {
//            item.setCertificateCode(residenceInfo.getResidencePermitNumber());
//        }
    }

    /**
     * 查房产信息
     *
     * @param item 查询条件
     */
    private void getHousePropertyVoInfo(PublicSecurityAndHousePropertyVo item) {
        //房产审核状态
        if (ObjectUtil.isNotEmpty(item.getEstateReviewStatus())) {
            item.setEstateReviewStatus(AuditEnum.getMessageByCode(item.getEstateReviewStatus()));
        }
        item.setType("");
        String countyHouseName = deptProperties.getName() + "房产-";
        String lastSql = "LIMIT 1";
        //房产信息类型（8：房屋所有权证9：不动产权证10：土地使用证11：购房合同12：抵押合同13：集资房14：小产权房15：廉租房/经济保障房16：宅基证17：自然村）
        //房屋所有产权
        if (HouseTypeEnum.HOUSEOWNERSHIP.getCode().equals(item.getHouseInfoType())) {

            HouseOwnershipInfo info = this.getOne(new LambdaUpdateWrapper<HouseOwnershipInfo>()
                    .eq(HouseOwnershipInfo::getStudentId, item.getStudentBaseId())
                    .last(lastSql));

            if (ObjectUtil.isNotEmpty(info)) {
                item.setResidencePermitName(info.getHumanName());
                item.setCertificateCode(info.getHouseNumber());
                item.setType(countyHouseName + HouseTypeEnum.HOUSEOWNERSHIP.getMessage());
                item.setRelationOfStudent(info.getRelationOfStudent());
            }

        } else if (HouseTypeEnum.ESTATE.getCode().equals(item.getHouseInfoType())) {

            EstateInfo info = estateInfoService.getOne(new LambdaUpdateWrapper<EstateInfo>()
                    .eq(EstateInfo::getStudentId, item.getStudentBaseId())
                    .last(lastSql));

            if (ObjectUtil.isNotEmpty(info)) {
                item.setResidencePermitName(info.getHumanName());
                item.setCertificateCode(info.getUnitNumber());
                item.setType(countyHouseName + HouseTypeEnum.ESTATE.getMessage());
                item.setRelationOfStudent(info.getRelationOfStudent());
            }
        } else if (HouseTypeEnum.LANDUSE.getCode().equals(item.getHouseInfoType())) {

            LandUseInfo info = landUseInfoService.getOne(new LambdaUpdateWrapper<LandUseInfo>()
                    .eq(LandUseInfo::getStudentId, item.getStudentBaseId())
                    .last(lastSql));

            if (ObjectUtil.isNotEmpty(info)) {
                item.setResidencePermitName(info.getHumanName());
                item.setCertificateCode(info.getEstateNumber());
                item.setType(countyHouseName + HouseTypeEnum.LANDUSE.getMessage());
                item.setRelationOfStudent(info.getRelationOfStudent());
            }

        } else if (HouseTypeEnum.HOUSEPURCHASECONTRACT.getCode().equals(item.getHouseInfoType())) {

            HousePurchaseContractInfo info = housePurchaseContractInfoService.getOne(new LambdaUpdateWrapper<HousePurchaseContractInfo>()
                    .eq(HousePurchaseContractInfo::getStudentId, item.getStudentBaseId())
                    .last(lastSql));

            if (ObjectUtil.isNotEmpty(info)) {
                item.setResidencePermitName(info.getPurchaseName());
                item.setCertificateCode(info.getContractNumber());
                item.setType(countyHouseName + HouseTypeEnum.HOUSEPURCHASECONTRACT.getMessage());
                item.setRelationOfStudent(info.getRelationOfStudent());
            }

        } else if (HouseTypeEnum.MORTGAGECONTRACT.getCode().equals(item.getHouseInfoType())) {

            MortgageContractInfo info = mortgageContractInfoService.getOne(new LambdaUpdateWrapper<MortgageContractInfo>()
                    .eq(MortgageContractInfo::getStudentId, item.getStudentBaseId())
                    .last(lastSql));

            if (ObjectUtil.isNotEmpty(info)) {
                item.setResidencePermitName(info.getHouseholderName());
                item.setCertificateCode(info.getMortgageeName());
                item.setType(countyHouseName + HouseTypeEnum.MORTGAGECONTRACT.getMessage());
                item.setRelationOfStudent(info.getRelationOfStudent());
            }

        } else if (HouseTypeEnum.RAISEFUNDS.getCode().equals(item.getHouseInfoType())) {

            RaiseFundsInfo info = raiseFundsInfoService.getOne(new LambdaUpdateWrapper<RaiseFundsInfo>()
                    .eq(RaiseFundsInfo::getStudentId, item.getStudentBaseId())
                    .last(lastSql));

            if (ObjectUtil.isNotEmpty(info)) {
                item.setResidencePermitName(info.getHouseholderName());
                item.setCertificateCode(info.getHouseNumber());
                item.setType(countyHouseName + HouseTypeEnum.RAISEFUNDS.getMessage());
                item.setRelationOfStudent(info.getRelationOfStudent());
            }

        } else if (HouseTypeEnum.SMALLPROPERTY.getCode().equals(item.getHouseInfoType())) {

            SmallPropertyInfo info = smallPropertyInfoService.getOne(new LambdaUpdateWrapper<SmallPropertyInfo>()
                    .eq(SmallPropertyInfo::getStudentId, item.getStudentBaseId())
                    .last(lastSql));

            if (ObjectUtil.isNotEmpty(info)) {
                item.setResidencePermitName(info.getHouseholderName());
                item.setType(countyHouseName + HouseTypeEnum.SMALLPROPERTY.getMessage());
                item.setRelationOfStudent(info.getRelationOfStudent());
            }

        } else if (HouseTypeEnum.LEASEHOSE.getCode().equals(item.getHouseInfoType())) {

            LeaseHouseInfo info = leaseHouseInfoService.getOne(new LambdaUpdateWrapper<LeaseHouseInfo>()
                    .eq(LeaseHouseInfo::getStudentId, item.getStudentBaseId())
                    .last(lastSql));

            if (ObjectUtil.isNotEmpty(info)) {
                item.setResidencePermitName(info.getHumanName());
                item.setType(countyHouseName + HouseTypeEnum.LEASEHOSE.getMessage());
                item.setRelationOfStudent(info.getRelationOfStudent());
            }

        } else if (HouseTypeEnum.HOMESTEADLICENSE.getCode().equals(item.getHouseInfoType())) {

            HomesteadLicenseInfo info = homesteadLicenseInfoService.getOne(new LambdaUpdateWrapper<HomesteadLicenseInfo>()
                    .eq(HomesteadLicenseInfo::getStudentId, item.getStudentBaseId())
                    .last(lastSql));

            if (ObjectUtil.isNotEmpty(info)) {
                item.setResidencePermitName(info.getHouseholderName());
                item.setType(countyHouseName + HouseTypeEnum.HOMESTEADLICENSE.getMessage());
                item.setRelationOfStudent(info.getRelationOfStudent());
            }

        } else if (HouseTypeEnum.NATURALVILLAGE.getCode().equals(item.getHouseInfoType())) {

            NaturalVillageInfo info = naturalVillageInfoService.getOne(new LambdaUpdateWrapper<NaturalVillageInfo>()
                    .eq(NaturalVillageInfo::getStudentId, item.getStudentBaseId())
                    .last(lastSql));

            if (ObjectUtil.isNotEmpty(info)) {
                item.setResidencePermitName(info.getHouseholderName());
                item.setType(countyHouseName + HouseTypeEnum.NATURALVILLAGE.getMessage());
                item.setRelationOfStudent(info.getRelationOfStudent());
            }

        }
    }


}
