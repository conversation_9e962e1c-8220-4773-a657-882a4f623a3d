package com.shida.region.biz.controller;


import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.annotation.SaMode;

import com.shida.autolog.AutoLog;
import com.shida.autolog.LogType;
import com.shida.autolog.OperateType;
import com.shida.constant.RoleConstant;

import com.shida.pojo.form.KeyForm;
import com.shida.region.biz.pojo.from.EnrollQueryForm;

import com.shida.region.biz.pojo.from.LuanZhouKeyForm;
import com.shida.region.biz.pojo.vo.EnrollQueryVo;
import com.shida.region.biz.pojo.vo.EnrollRequisitionVo;

import com.shida.region.biz.service.IEnrollRequisitionService;
import com.shida.region.biz.service.IRegistrationTimeService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


import javax.annotation.Resource;
import javax.validation.Valid;



/**
 * <AUTHOR>
 * @Date 2024/4/18 17:37
 */
@Tag(name = "录取查询")
@RestController
@RequestMapping("/biz/enrollQuery")
public class EnrollQueryController {

    @Resource
    private IRegistrationTimeService registrationTimeService;
    @Resource
    private IEnrollRequisitionService enquisitionService;


    @SaCheckRole(value = {RoleConstant.PARENT_CODE}, mode = SaMode.OR)
    @Operation(summary = "录取查询第一显示页面:参数userId")
    @PostMapping("/onePage")
    public EnrollQueryVo enrollQueryOnePage(@RequestBody @Valid LuanZhouKeyForm<Long> keyForm) {
        return registrationTimeService.enrollQueryOnePage(keyForm);
    }

//    @SaCheckRole(value = {RoleConstant.PARENT_CODE}, mode = SaMode.OR)
//    @Operation(summary = "通过身份证号搜索框查询")
//    @PostMapping("/search")
//    public EnrollQuerySearchTypeVo searchByIdCardNumber(@RequestBody @Valid EnrollQuerySearchForm form) {
//        return registrationTimeService.searchByIdCardNumber(form);
//    }

//    @SaCheckRole(value = {RoleConstant.PARENT_CODE}, mode = SaMode.OR)
//    @AutoLog(operateType = OperateType.OPERATE_ADD)
//    @Operation(summary = "搜索框查询弹窗确定:学生绑定微信")
//    @PostMapping("/stuBindingWeChat")
//    public void stuBindingWeChat(@RequestBody @Valid StuBindingWeChatForm form) {
//         registrationTimeService.stuBindingWeChat(form);
//    }

    @SaCheckRole(value = {RoleConstant.PARENT_CODE}, mode = SaMode.OR)
    @Operation(summary = "录取通知书:参数studentId")
    @PostMapping("/requisition")
    public EnrollRequisitionVo enrollRequisition(@RequestBody @Valid KeyForm<Long> keyForm) {
        return enquisitionService.enrollRequisition(keyForm.getKey());
    }

    @SaCheckRole(value = {RoleConstant.SCHOOL_CODE}, mode = SaMode.OR)
    @Operation(summary = "发送录取通知书")
    @PostMapping("/sendRequisition")
    @AutoLog(logType = LogType.OPERATE, operateType = OperateType.OPERATE_ADD)
    public int sendEnrollRequisition(@RequestBody @Valid EnrollQueryForm form) {
        return enquisitionService.sendEnrollRequisition(form);
    }


}
