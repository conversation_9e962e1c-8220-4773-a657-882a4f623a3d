package com.shida.region.biz.service;

import com.shida.pojo.form.KeyForm;
import com.shida.region.biz.entity.InfoRegistrationConfigShow;
import com.baomidou.mybatisplus.extension.service.IService;
import com.shida.region.biz.pojo.from.SetRootConfigIsShow;
import com.shida.region.biz.pojo.vo.ConfigIsShowVo;

import java.util.List;

/**
 * <p>
 * 配置项显示表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-25
 */
public interface IInfoRegistrationConfigShowService extends IService<InfoRegistrationConfigShow> {

    List<ConfigIsShowVo> getRootConfigIsShow(KeyForm<Long> form);

    boolean setRootConfigIsShow(SetRootConfigIsShow form);
}
