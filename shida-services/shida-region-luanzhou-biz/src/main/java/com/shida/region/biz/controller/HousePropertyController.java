package com.shida.region.biz.controller;


import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.annotation.SaMode;
import com.shida.autolog.AutoLog;
import com.shida.autolog.OperateType;
import com.shida.constant.RoleConstant;
import com.shida.pojo.vo.PageInfo;
import com.shida.region.biz.pojo.search.PublicSecurityAndHouseSearch;
import com.shida.region.biz.pojo.vo.PublicSecurityAndHousePropertyVo;
import com.shida.region.biz.service.IHouseOwnershipInfoService;
import com.shida.region.biz.service.IStudentBaseService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;

/**
 * <AUTHOR>
 * @Date 2024/4/18 17:37
 */
@Tag(name = "房管信息核对")
@RestController
@RequestMapping("/biz/house/property")
public class HousePropertyController {

    @Resource
    private IStudentBaseService studentBaseService;
    @Resource
    private IHouseOwnershipInfoService houseOwnershipInfoService;

    @SaCheckRole(value = {RoleConstant.HOUSE_CODE,RoleConstant.COUNTY_ADMIN_CODE}, mode = SaMode.OR)
    @Operation(summary = "房管信息核对分页")
    @PostMapping("page")
    public PageInfo<PublicSecurityAndHousePropertyVo> page(@RequestBody @Valid PublicSecurityAndHouseSearch search) {
        return houseOwnershipInfoService.getPublicSecurityAndHousePropertyPage(search);
    }


    @Operation(summary = "导出房管信息")
    @PostMapping("/export")
    @SaCheckRole(value = {RoleConstant.HOUSE_CODE,RoleConstant.COUNTY_ADMIN_CODE}, mode = SaMode.OR)
    @AutoLog(operateType = OperateType.OPERATE_IMPORT)
    public void export(@RequestBody @Valid PublicSecurityAndHouseSearch search, HttpServletResponse response) throws IOException {
        houseOwnershipInfoService.exportHouseProperty(search,response);
    }


    @PostMapping("/import")
    @Operation(summary = "导入房管信息")
    @SaCheckRole(value = {RoleConstant.HOUSE_CODE,RoleConstant.COUNTY_ADMIN_CODE}, mode = SaMode.OR)
    @AutoLog(operateType = OperateType.OPERATE_IMPORT)
    public String importHouseProperty(MultipartFile file)  {
        houseOwnershipInfoService.importHouseProperty(file);
        return "success";
    }

}
