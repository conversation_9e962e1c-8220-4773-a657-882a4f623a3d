package com.shida.region.biz.controller;


import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.annotation.SaMode;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.shida.autolog.AutoLog;
import com.shida.autolog.OperateType;
import com.shida.constant.RoleConstant;
import com.shida.enums.RestCode;
import com.shida.exceptions.ServiceException;
import com.shida.pojo.form.KeyForm;
import com.shida.pojo.vo.PageInfo;
import com.shida.region.biz.entity.StudentBase;
import com.shida.region.biz.entity.StudentBaseInfo;
import com.shida.region.biz.pojo.from.*;
import com.shida.region.biz.pojo.search.RecruitStudentSearch;
import com.shida.region.biz.pojo.vo.AuditStatusVo;
import com.shida.region.biz.pojo.vo.RecruitStudentDetailsVo;
import com.shida.region.biz.pojo.vo.RecruitStudentVo;
import com.shida.region.biz.service.IGuardianInfoService;
import com.shida.region.biz.service.IStudentBaseInfoService;
import com.shida.region.biz.service.IStudentBaseService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import static com.shida.constant.RoleConstant.*;

@Tag(name = "报名列表")
@RestController
@RequestMapping("/biz/recruitStudent")
public class RecruitStudentController {


    @Resource
    private IStudentBaseInfoService studentBaseInfoService;

    @Resource
    private IStudentBaseService studentBaseService;
    @Resource
    private IGuardianInfoService guardianInfoService;


    /**
     * 查询 报名类别
     *
     * @param form
     * @return
     */
    @PostMapping("type")
    @Operation(summary = "报名类别查询")
    @SaCheckRole(value = {RoleConstant.SUPER_ADMIN_CODE, COUNTY_ADMIN_CODE, AUDITOR_CODE, SCHOOL_CODE, CITY_ADMIN_CODE}, mode = SaMode.OR)
    public List<String> type(@RequestBody @Valid KeyForm<Integer> form) {
        return studentBaseInfoService.type(form.getKey());
    }

    /**
     * 报名列表 查询
     *
     * @return
     */
    @PostMapping("page")
    @Operation(summary = "报名列表")
    @SaCheckRole(value = {RoleConstant.SUPER_ADMIN_CODE, COUNTY_ADMIN_CODE, AUDITOR_CODE, SCHOOL_CODE, CITY_ADMIN_CODE}, mode = SaMode.OR)
    public PageInfo<RecruitStudentVo> page(@RequestBody @Valid RecruitStudentSearch search) {
        return studentBaseInfoService.getRecruitStudentPage(search);
    }
    /**
     * 报名列表 查询
     *
     * @return
     */
    @PostMapping("pageS")
    @Operation(summary = "报名列表")
    @SaCheckRole(value = {RoleConstant.SUPER_ADMIN_CODE, COUNTY_ADMIN_CODE, AUDITOR_CODE, SCHOOL_CODE, CITY_ADMIN_CODE}, mode = SaMode.OR)
    public PageInfo<RecruitStudentVo> pageS(@RequestBody @Valid RecruitStudentSearch search) {
        return studentBaseInfoService.getRecruitStudentPageS(search);
    }

    /**
     * 报名列表详情
     *
     * @param form
     * @return
     */
    @Operation(summary = "报名列表详情")
    @PostMapping("details")
    @SaCheckRole(value = {RoleConstant.SUPER_ADMIN_CODE, COUNTY_ADMIN_CODE, AUDITOR_CODE, SCHOOL_CODE}, mode = SaMode.OR)
    public RecruitStudentDetailsVo details(@RequestBody @Valid KeyForm<Long> form) {
        return studentBaseInfoService.details(form.getKey());
    }


    /**
     * 审核通过
     *
     * @param auditForm
     * @return
     */
    @PostMapping("passAudit")
    @Operation(summary = "审核通过")
    @AutoLog(operateType = OperateType.OPERATE_EDIT)
    @SaCheckRole(value = {RoleConstant.SUPER_ADMIN_CODE, COUNTY_ADMIN_CODE, AUDITOR_CODE, SCHOOL_CODE}, mode = SaMode.OR)
    public Boolean passAudit(@RequestBody @Valid AuditForm auditForm) {

        return studentBaseInfoService.passAudit(auditForm);
    }


    /**
     * 批量审核
     *
     * @param form
     * @return
     */
    @PostMapping("batchAudit")
    @Operation(summary = "批量审核通过")
    @SaCheckRole(value = {RoleConstant.SUPER_ADMIN_CODE, COUNTY_ADMIN_CODE, AUDITOR_CODE}, mode = SaMode.OR)
    public Boolean batchAudit(@RequestBody @Valid AuditORBatchAuditForm form) {
        return studentBaseInfoService.batchAudit(form);

    }

    /**
     * 驳回
     *
     * @param form
     * @return
     */
    @PostMapping("noPass")
    @Operation(summary = "驳回")
    @AutoLog(operateType = OperateType.OPERATE_EDIT)
    @SaCheckRole(value = {RoleConstant.SUPER_ADMIN_CODE, COUNTY_ADMIN_CODE, AUDITOR_CODE, SCHOOL_CODE}, mode = SaMode.OR)
    public Boolean noPass(@RequestBody @Valid NoPassForm form) {
        return studentBaseInfoService.noPass(form);
    }


    /**
     * 调剂
     *
     * @param form
     * @return
     */
    @PostMapping("adjust")
    @Operation(summary = "调剂")
    @AutoLog(operateType = OperateType.OPERATE_EDIT)
    @SaCheckRole(value = {RoleConstant.SUPER_ADMIN_CODE, COUNTY_ADMIN_CODE, AUDITOR_CODE}, mode = SaMode.OR)
    public Boolean adjust(@RequestBody @Valid AdjustForm form) {
        return studentBaseInfoService.adjust(form);
    }


    /**
     * 公示录取结果
     *
     * @param form
     * @return
     */
    @PostMapping("publicAdmissionResults")
    @Operation(summary = "公示录取结果")
    @AutoLog(operateType = OperateType.OPERATE_EDIT)
    @SaCheckRole(value = {RoleConstant.SUPER_ADMIN_CODE, COUNTY_ADMIN_CODE, AUDITOR_CODE, SCHOOL_CODE}, mode = SaMode.OR)
    public Boolean publicAdmissionResults(@RequestBody @Valid AuditORBatchAuditForm form) {
        return studentBaseInfoService.publicAdmissionResults(form);
    }

    @PostMapping("resetPublicStudent")
    @Operation(summary = "取消公示录取结果")
    @AutoLog(operateType = OperateType.OPERATE_EDIT)
    @SaCheckRole(value = {RoleConstant.COUNTY_ADMIN_CODE}, mode = SaMode.OR)
    public Boolean resetPublicStudent(@RequestBody @Valid ResetPublicForm form) {
        return guardianInfoService.resetPublicStudent(form);
    }


    /**
     * 公示
     *
     * @param form
     * @return
     */
    @PostMapping("publicity")
    @Operation(summary = "公示")
    @SaCheckRole(value = {RoleConstant.SUPER_ADMIN_CODE, COUNTY_ADMIN_CODE, AUDITOR_CODE,SCHOOL_CODE}, mode = SaMode.OR)
    public String publicity(@RequestBody @Valid AuditORBatchAuditForm form) {
        return studentBaseInfoService.publicity(form);
    }


    /**
     * 是否报道
     *
     * @param form
     * @return
     */
    @PostMapping("report")
    @Operation(summary = "报道")
    @AutoLog(operateType = OperateType.OPERATE_EDIT)
    @SaCheckRole(value = {RoleConstant.SUPER_ADMIN_CODE, COUNTY_ADMIN_CODE, AUDITOR_CODE, SCHOOL_CODE}, mode = SaMode.OR)
    public Boolean report(@RequestBody @Valid ReportForm form) {
        return studentBaseInfoService.report(form);
    }


    /**
     * 修改是否优抚对象
     *
     * @param form
     * @return
     */
    @PostMapping("updateEntitledGroup")
    @Operation(summary = "修改是否优抚对象")
    @AutoLog(operateType = OperateType.OPERATE_EDIT)
    @SaCheckRole(value = {RoleConstant.SUPER_ADMIN_CODE, COUNTY_ADMIN_CODE, AUDITOR_CODE, SCHOOL_CODE}, mode = SaMode.OR)
    public Boolean updateEntitledGroup(@RequestBody @Valid UpdateEntitledGroupForm form) {

        return studentBaseInfoService.updateEntitledGroup(form);
    }


    /**
     * 报名 审核状态
     *
     * @param keyForm
     * @return
     */
    @PostMapping("auditStatus")
    @Operation(summary = "详情审核内容")
    @AutoLog(operateType = OperateType.OPERATE_EDIT)
    @SaCheckRole(value = {RoleConstant.SUPER_ADMIN_CODE, COUNTY_ADMIN_CODE, AUDITOR_CODE, SCHOOL_CODE}, mode = SaMode.OR)
    public List<AuditStatusVo> auditStatus(@RequestBody @Valid KeyForm<Long> keyForm) {

        return studentBaseInfoService.auditStatus(keyForm.getKey());
    }


    /**
     * 导出报名信息
     *
     * @param search
     * @param response
     * @throws IOException
     */
    @PostMapping("exportInformationOfRegistration")
    @Operation(summary = "导出报名信息")
    @AutoLog(operateType = OperateType.OPERATE_EXPORT)
    @SaCheckRole(value = {RoleConstant.SUPER_ADMIN_CODE, COUNTY_ADMIN_CODE, AUDITOR_CODE, SCHOOL_CODE}, mode = SaMode.OR)
    public void exportInformationOfRegistration(@RequestBody @Valid RecruitStudentSearch search
            , HttpServletResponse response) throws IOException {

        studentBaseInfoService.exportInformationOfRegistration(search, response);
//        studentBaseInfoService.waczExportStudent(search,response);

    }


    @PostMapping("exportHouseholdSurvey")
    @Operation(summary = "导出入户调查单")
    @AutoLog(operateType = OperateType.OPERATE_EXPORT)
    @SaCheckRole(value = {RoleConstant.SUPER_ADMIN_CODE, COUNTY_ADMIN_CODE, AUDITOR_CODE, SCHOOL_CODE}, mode = SaMode.OR)
    public void exportHouseholdSurvey(HttpServletResponse response, @RequestBody @Valid KeyForm<Long> keyForm) throws IOException {

        //根据ID 查询 学生数据,
        StudentBase studentBase = studentBaseService.getById(keyForm.getKey());

        if (ObjectUtil.isEmpty(studentBase.getPath())) {
            throw new ServiceException(RestCode.DATA_ERROR, "文件路径异常");
        }

        LambdaQueryWrapper<StudentBaseInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(StudentBaseInfo::getStudentId, studentBase.getId());
        StudentBaseInfo studentBaseInfo = studentBaseInfoService.getOne(lambdaQueryWrapper);

        String fileName = "入户调查单.zip";
        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));

        ZipOutputStream zipOutputStream = new ZipOutputStream(response.getOutputStream());

        byte[] buf = new byte[1024];

        //数据存放位置
        File file = new File(studentBase.getPath());
//            File file = new File(studentBase.getOtherPath() + StudentBaseInfo.getstuName + StudentBaseInfo.getstuIdNumber);
        if (file.exists()) {
            zipOutputStream.putNextEntry(new ZipEntry(studentBaseInfo.getStudentName() + "-" + studentBaseInfo.getStudentIdCardNumber() + ".xlsx"));
            FileInputStream inputStream = new FileInputStream(file);
            int len;
            while ((len = inputStream.read(buf)) > 0) {
                zipOutputStream.write(buf, 0, len);
            }
        }

        zipOutputStream.close();
    }


    @PostMapping("batchExportHouseholdSurvey")
    @Operation(summary = "批量导出入户调查单")
    @SaCheckRole(value = {RoleConstant.SUPER_ADMIN_CODE, COUNTY_ADMIN_CODE, AUDITOR_CODE, SCHOOL_CODE}, mode = SaMode.OR)
    public void batchExportHouseholdSurvey(HttpServletResponse response,
                                           @RequestBody @Valid BatchExportHouseholdSurveyForm batchExportHouseholdSurveyForm
    ) throws Exception {


        studentBaseInfoService.batchHouseholdSurveyForm(response, batchExportHouseholdSurveyForm.getList(), batchExportHouseholdSurveyForm.getSearch());


//        List<Long> key = form.getKey();
//
//        List<BatchExportHouseholdSurveyVo> batchExportHouseholdSurveyVos = studentBaseInfoService.batchExportHouseholdSurvey(key);
//
//        for (int i = 0; i < batchExportHouseholdSurveyVos.size(); i++) {
//
//            if(ObjectUtil.isEmpty(batchExportHouseholdSurveyVos.get(i).getPath())){
//                throw new ServiceException("第" + i + 1 + "条数据路径异常");
//            }
//        }
//
//
//        String fileName = "入户调查单.zip";
//        response.setContentType("application/octet-stream");
//        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
//
//        ZipOutputStream zipOutputStream = new ZipOutputStream(response.getOutputStream());
//
//        byte[] buf = new byte[1024];
//        for (BatchExportHouseholdSurveyVo batchExportHouseholdSurveyVo : batchExportHouseholdSurveyVos) {
//            //数据存放位置
//            File file = new File(batchExportHouseholdSurveyVo.getPath());
//            if (file.exists()) {
//                zipOutputStream.putNextEntry(new ZipEntry(batchExportHouseholdSurveyVo.getStudentName() + "-" + batchExportHouseholdSurveyVo.getStudentIdCardNumber() + ".xlsx"));
//                FileInputStream inputStream = new FileInputStream(file);
//                int len;
//                while ((len = inputStream.read(buf)) > 0) {
//                    zipOutputStream.write(buf, 0, len);
//                }
//            }
//        }
//        zipOutputStream.close();

    }

}
