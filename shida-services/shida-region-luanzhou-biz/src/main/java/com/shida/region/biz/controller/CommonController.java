package com.shida.region.biz.controller;

import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.annotation.SaMode;
import com.shida.autolog.AutoLog;
import com.shida.autolog.OperateType;
import com.shida.constant.RoleConstant;
import com.shida.region.biz.pojo.from.EnrollQuerySearchForm;
import com.shida.region.biz.pojo.from.StudentBindingForm;
import com.shida.region.biz.pojo.vo.EnrollQuerySearchTypeVo;
import com.shida.region.biz.service.ICommonStudentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @Date 2025/4/7 16:42
 * @PackageName:com.shida.region.biz.controller
 * @ClassName: CommonController
 * @Version 1.0
 */
@Tag(name = "公民同报")
@RestController
@RequestMapping("/common/biz/parent")
public class CommonController {

    @Resource
    private ICommonStudentService commonStudentService;

    @SaCheckRole(value = {RoleConstant.PARENT_CODE}, mode = SaMode.OR)
    @Operation(summary = "通过身份证号搜索框查询")
    @PostMapping("/search")
    public EnrollQuerySearchTypeVo searchByIdCardNumber(@RequestBody @Valid EnrollQuerySearchForm form) {
        return commonStudentService.searchByIdCardNumber(form);
    }

    @SaCheckRole(value = {RoleConstant.PARENT_CODE}, mode = SaMode.OR)
    @AutoLog(operateType = OperateType.OPERATE_ADD)
    @Operation(summary = "搜索框查询弹窗确定:学生绑定微信")
    @PostMapping("/stuBindingWeChat")
    public Boolean stuBindingWeChat(@RequestBody @Valid StudentBindingForm form) {
        return commonStudentService.studentBindingWeChat(form);
    }
}
