package com.shida.region.biz.service;

import com.shida.region.biz.pojo.excel.ExcelBatchImportResponse;
import com.shida.region.biz.pojo.from.DeleteStudentForm;
import com.shida.region.biz.pojo.from.RecoverClearEnrollStuInfoForm;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/10 16:28
 * @PackageName:com.shida.region.biz.service
 * @ClassName: IDeletePrivateeStudentService
 * @Version 1.0
 */
public interface IDeletePrivateeStudentService {

    Boolean clearEnrollInfo(DeleteStudentForm form);

    List<ExcelBatchImportResponse> importBatchClearStuExcel(MultipartFile file) throws IOException;

    void exportBatchClearStuExcel(HttpServletResponse response);

    Boolean recoverEnrollInfo(@Valid RecoverClearEnrollStuInfoForm form);
}
