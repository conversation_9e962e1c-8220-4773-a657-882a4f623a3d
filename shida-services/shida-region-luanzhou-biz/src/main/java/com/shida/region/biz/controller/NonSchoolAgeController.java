package com.shida.region.biz.controller;

import cn.dev33.satoken.annotation.SaCheckRole;
import com.shida.autolog.AutoLog;
import com.shida.autolog.LogType;
import com.shida.autolog.OperateType;
import com.shida.constant.RoleConstant;
import com.shida.pojo.form.KeyForm;
import com.shida.pojo.search.Search;
import com.shida.pojo.vo.PageInfo;
import com.shida.region.biz.pojo.from.AddNonSchoolAgeInfoForm;
import com.shida.region.biz.pojo.vo.NonSchoolAgePageVo;
import com.shida.region.biz.service.INonSchoolAgeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * Description:
 *
 * <AUTHOR>
 * @Create 2025/2/18 9:13
 */
@RestController
@RequestMapping("/biz/NonSchoolAge")
@Tag(name = "非适龄儿童报名信息")
public class NonSchoolAgeController {

    @Resource
    private INonSchoolAgeService nonSchoolAgeService;

    @PostMapping("add")
    @Operation(summary = "新增")
    @SaCheckRole(RoleConstant.COUNTY_ADMIN_CODE)
    @AutoLog(logType = LogType.OPERATE,operateType = OperateType.OPERATE_ADD)
    public String add(@RequestBody @Valid AddNonSchoolAgeInfoForm form){
        return nonSchoolAgeService.add(form);
    }

    @PostMapping("getPage")
    @Operation(summary = "查询分页")
    @SaCheckRole(RoleConstant.COUNTY_ADMIN_CODE)
    public PageInfo<NonSchoolAgePageVo> getPage(@RequestBody Search search){
        return nonSchoolAgeService.getPage(search);
    }

    @PostMapping("delete")
    @Operation(summary = "删除")
    @SaCheckRole(RoleConstant.COUNTY_ADMIN_CODE)
    @AutoLog(logType = LogType.OPERATE,operateType = OperateType.OPERATE_DELETE)
    public String delete(@RequestBody @Valid KeyForm<Long> form){
        return nonSchoolAgeService.delete(form.getKey());
    }




}
