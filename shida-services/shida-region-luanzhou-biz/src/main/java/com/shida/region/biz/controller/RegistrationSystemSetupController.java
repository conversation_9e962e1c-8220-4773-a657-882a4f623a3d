package com.shida.region.biz.controller;


import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.annotation.SaMode;
import com.shida.autolog.AutoLog;
import com.shida.autolog.OperateType;
import com.shida.constant.RoleConstant;
import com.shida.noRepeatSubmit.NoRepeatSubmit;
import com.shida.region.biz.pojo.from.SaveSystemSetUpTreeForm;
import com.shida.region.biz.pojo.vo.RegistrationSystemSetupVo;
import com.shida.region.biz.service.IRegistrationSystemSetupService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;


@Tag(name = "报名系统设置")
@RestController
@RequestMapping("/biz/registrationSystemSetup")
public class RegistrationSystemSetupController {

    @Resource
    private IRegistrationSystemSetupService registrationSystemSetupService;


    /**
     * 系统设置 树状图
     * @return
     */
    @Operation(summary = "查询系统设置树状图")
    @SaCheckRole(value = {RoleConstant.SUPER_ADMIN_CODE,RoleConstant.CITY_ADMIN_CODE},mode = SaMode.OR)
    @PostMapping("getSystemSetUpTree")
    public List<RegistrationSystemSetupVo> getSystemSetUpTree() {
        return registrationSystemSetupService.getSystemSetUpTree();
    }


    /**
     * 保存系统设置
     * @param form
     * @return
     */
    @NoRepeatSubmit
    @Operation(summary = "保存系统设置")
    @AutoLog(operateType = OperateType.OPERATE_ADD)
    @SaCheckRole(value = {RoleConstant.SUPER_ADMIN_CODE,RoleConstant.CITY_ADMIN_CODE},mode = SaMode.OR)
    @PostMapping("saveSystemSetUp")
    public Boolean saveSystemSetUp(@RequestBody @Valid SaveSystemSetUpTreeForm form){
        return registrationSystemSetupService.saveSystemSetUp(form);
    }

    /**
     * 回显保存系统设置
     * @param
     * @return
     */
    @Operation(summary = "勾选回显")
    @SaCheckRole(value = {RoleConstant.SUPER_ADMIN_CODE,RoleConstant.CITY_ADMIN_CODE},mode = SaMode.OR)
    @PostMapping("/echoSystemSetUpTree")
    public List<Long> echoSystemSetUpTree(){

        return registrationSystemSetupService.echoSystemSetUpTree();
    }


    /**
     * 功能大纲 树 查询
     * @param
     * @return
     */
    @PostMapping("functionalOutlineTree")
    @Operation(summary = "功能大纲")
    @SaCheckRole(value = {RoleConstant.SUPER_ADMIN_CODE,RoleConstant.CITY_ADMIN_CODE},mode = SaMode.OR)
    public List<RegistrationSystemSetupVo> functionalOutlineTree(){

        return registrationSystemSetupService.functionalOutlineTree();
    }


    /**
     * 报名设置列表回显
     * @return
     */
    @Operation(summary = "报名设置列表回显")
    @PostMapping("echoSignUpSetUpList")
    @SaCheckRole(value = {RoleConstant.SUPER_ADMIN_CODE,RoleConstant.CITY_ADMIN_CODE},mode = SaMode.OR)
    public  Map<String,Object>  echoSignUpSetUpList(){


        return registrationSystemSetupService.echoSignUpSetUpList();
    }
}
