package com.shida.region.biz.controller;


import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.annotation.SaMode;
import com.shida.autolog.AutoLog;
import com.shida.autolog.OperateType;
import com.shida.constant.RoleConstant;
import com.shida.region.biz.pojo.from.AdjustmentForm;
import com.shida.region.biz.service.IAdjustmentService;
import com.shida.region.biz.service.IStudentBaseInfoService;
import com.shida.region.biz.service.IStudentBaseService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;


/**
 * <AUTHOR>
 * @Date 2024/4/18 17:37
 */
@Tag(name = "魏县-派位")
@RestController
@RequestMapping("/biz/weiXian")
public class WeiXianController {

    @Resource
    private IStudentBaseInfoService studentBaseInfoService;



    @SaCheckRole(value = {RoleConstant.LOCATION}, mode = SaMode.OR)
    @AutoLog(operateType = OperateType.OPERATE_EXPORT)
    @Operation(summary = "导出派位名单")
    @PostMapping("/exportPaiWei")
    public void exportWeiXianPaiWei(HttpServletResponse response) {
        studentBaseInfoService.exportWeiXianPaiWei(response);
    }



}
