package com.shida.region.biz.controller;

import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.annotation.SaMode;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.shida.autolog.AutoLog;
import com.shida.autolog.LogType;
import com.shida.autolog.OperateType;
import com.shida.constant.RoleConstant;
import com.shida.enums.RestCode;
import com.shida.exceptions.ServiceException;
import com.shida.pojo.form.KeyForm;
import com.shida.properties.UploadFileProperties;
import com.shida.properties.UploadUrlProperties;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.nio.file.Paths;

/**
 * Description:
 *
 * @Author: laobaduo
 * @Create: 2024/4/29 - 16:24
 */
@RestController
@RequestMapping("/biz/file/")
@Tag(name = "文件管理")
public class FileController {

    @Resource
    private UploadFileProperties uploadFileProperties;
    @Resource
    private UploadUrlProperties uploadUrlProperties;

    @PostMapping("uploadImg")
    @AutoLog(logType = LogType.OPERATE, operateType = OperateType.OPERATE_ADD)
    @Operation(summary = "上传图片通用接口")
    @SaCheckRole(value = {RoleConstant.SUPER_ADMIN_CODE, RoleConstant.CITY_ADMIN_CODE, RoleConstant.COUNTY_ADMIN_CODE,
            RoleConstant.AUDITOR_CODE, RoleConstant.PARENT_CODE, RoleConstant.SCHOOL_CODE}, mode = SaMode.OR)
    public String uploadImg(@RequestParam("file") MultipartFile file) {
        if (null == file || file.isEmpty())
            return "上传图片为空！";
        try {
            String suffix = ".jpg";
            //源文件名
            String fileName = file.getOriginalFilename();
            if (fileName.lastIndexOf(".") > 0) {
                //后缀名
                suffix = fileName.substring(fileName.lastIndexOf("."));
            }

            String simpleUUID = IdUtil.simpleUUID();
            File destFile = new File(uploadFileProperties.getImagePath());
            if (!destFile.exists()) {
                destFile.mkdirs();
            }
            file.transferTo(Paths.get(uploadFileProperties.getImagePath() + simpleUUID + suffix));
            return uploadUrlProperties.getImage() + simpleUUID + suffix;
        } catch (IOException e) {
            throw new ServiceException(RestCode.DATA_ERROR,"上传图片出错！");
        }
    }

    @PostMapping("uploadAvatar")
    @AutoLog(logType = LogType.OPERATE, operateType = OperateType.OPERATE_ADD)
    @Operation(summary = "上传头像通用接口")
    @SaCheckRole(value = {RoleConstant.SUPER_ADMIN_CODE, RoleConstant.CITY_ADMIN_CODE, RoleConstant.COUNTY_ADMIN_CODE,
            RoleConstant.AUDITOR_CODE, RoleConstant.PARENT_CODE, RoleConstant.SCHOOL_CODE}, mode = SaMode.OR)
    public String uploadAvatar(@RequestParam("file") MultipartFile file) {
        if (null == file || file.isEmpty())
            return "上传头像为空！";
        try {
            //源文件名
            String fileName = file.getOriginalFilename();
            //后缀名
            String suffix = fileName.substring(fileName.lastIndexOf("."));
            String simpleUUID = IdUtil.simpleUUID();
            File destFile = new File(uploadFileProperties.getAvatarPath());
            if (!destFile.exists()) {
                destFile.mkdirs();
            }
            file.transferTo(Paths.get(uploadFileProperties.getAvatarPath() + simpleUUID + suffix));
            return uploadUrlProperties.getAvatar() + simpleUUID + suffix;
        } catch (IOException e) {
            throw new ServiceException(RestCode.DATA_ERROR,"上传头像出错！");
        }
    }

    @PostMapping("uploadOther")
    @AutoLog(logType = LogType.OPERATE, operateType = OperateType.OPERATE_ADD)
    @Operation(summary = "上传文件通用接口")
    @SaCheckRole(value = {RoleConstant.SUPER_ADMIN_CODE, RoleConstant.CITY_ADMIN_CODE, RoleConstant.COUNTY_ADMIN_CODE,
            RoleConstant.AUDITOR_CODE, RoleConstant.PARENT_CODE, RoleConstant.SCHOOL_CODE}, mode = SaMode.OR)
    public String uploadOther(@RequestParam("file") MultipartFile file) {
        if (null == file || file.isEmpty())
            return "上传文件为空！";
        try {
            //源文件名
            String fileName = file.getOriginalFilename();
            //后缀名
            String suffix = fileName.substring(fileName.lastIndexOf("."));
            String simpleUUID = IdUtil.simpleUUID();
            File destFile = new File(uploadFileProperties.getOtherPath());
            if (!destFile.exists()) {
                destFile.mkdirs();
            }
            file.transferTo(Paths.get(uploadFileProperties.getOtherPath() + simpleUUID + suffix));
            return uploadUrlProperties.getOther() + simpleUUID + suffix;
        } catch (IOException e) {
            throw new ServiceException(RestCode.DATA_ERROR,"上传文件出错！");
        }
    }

    @PostMapping("deleteImg")
    @AutoLog(logType = LogType.OPERATE, operateType = OperateType.OPERATE_DELETE)
    @Operation(summary = "删除图片通用接口")
    @SaCheckRole(value = {RoleConstant.SUPER_ADMIN_CODE, RoleConstant.CITY_ADMIN_CODE, RoleConstant.COUNTY_ADMIN_CODE,
            RoleConstant.AUDITOR_CODE, RoleConstant.PARENT_CODE, RoleConstant.SCHOOL_CODE}, mode = SaMode.OR)
    public Boolean deleteImg(@RequestBody KeyForm<String> form) {
        String key = form.getKey();
        if(!key.startsWith(uploadUrlProperties.getImage()))
            throw new ServiceException(RestCode.DATA_ERROR,"图片存储路径有误！");
        String imgUrl = StrUtil.replace(key, uploadUrlProperties.getImage(), uploadFileProperties.getImagePath());
        File img = new File(imgUrl);
        if(img.exists()){
            return img.delete();
        }else{
            return true;
        }
    }

//    public static void main(String[] args) {
//        String url = "/upload/images/0f2d625c49ae4b1789b224cfa55f3f1b.jpg";
//        String imgUrl = StrUtil.replace(url,"/upload/images/", "/data/shida-region-biz-service/upload/images/");
//        File img = new File(imgUrl);
//        if(img.exists()){
//            System.out.println(img.delete());
//        }else{
//            throw new ServiceException("图片不存在！");
//        }
//    }


}
