package com.shida.region.biz.controller;


import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.annotation.SaMode;
import com.shida.autolog.AutoLog;
import com.shida.autolog.OperateType;
import com.shida.constant.RoleConstant;
import com.shida.pojo.vo.PageInfo;
import com.shida.region.biz.pojo.from.LuanZhouKeyForm;
import com.shida.region.biz.pojo.search.DelayStudentSearch;
import com.shida.region.biz.pojo.search.RecruitStudentSearch;
import com.shida.region.biz.pojo.vo.EnrollQueryVo;
import com.shida.region.biz.pojo.vo.RecruitStudentVo;
import com.shida.region.biz.service.IRegistrationTimeService;
import com.shida.region.biz.service.IStudentBaseInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import java.io.IOException;

import static com.shida.constant.RoleConstant.*;


/**
 * <AUTHOR>
 * @Date 2024/4/18 17:37
 */
@Tag(name = "延迟报名查询")
@RestController
@RequestMapping("/biz/delayQuery")
public class DelayQueryController {

    @Resource
    private IRegistrationTimeService registrationTimeService;
    @Resource
    private IStudentBaseInfoService studentBaseInfoService;


    @SaCheckRole(value = {RoleConstant.PARENT_CODE}, mode = SaMode.OR)
    @Operation(summary = "录取查询第一显示页面:参数userId")
    @PostMapping("/onePage")
    public EnrollQueryVo enrollQueryOnePage(@RequestBody @Valid LuanZhouKeyForm<Long> keyForm) {
        return registrationTimeService.delayQueryOnePage(keyForm.getKey(),keyForm.getDelayType());
    }

    /**
     * 延缓入学申请列表 查询
     * @return
     */
    @PostMapping("page")
    @Operation(summary = "延缓入学申请列表")
    @SaCheckRole(value = {RoleConstant.SUPER_ADMIN_CODE,COUNTY_ADMIN_CODE,SCHOOL_CODE},mode = SaMode.OR)
    public PageInfo<RecruitStudentVo> page(@RequestBody @Valid DelayStudentSearch search){
        return studentBaseInfoService.getDelayStudentPage(search);
    }
    /**
     * 导出延缓入学报名信息
     *
     * @param search
     * @param response
     * @throws IOException
     */
    @PostMapping("exportDelaySchoolStudent")
    @Operation(summary = "导出延缓入学报名信息")
    @AutoLog(operateType = OperateType.OPERATE_EXPORT)
    @SaCheckRole(value = {RoleConstant.SUPER_ADMIN_CODE, COUNTY_ADMIN_CODE, AUDITOR_CODE}, mode = SaMode.OR)
    public void exportDelaySchoolStudent(@RequestBody @Valid RecruitStudentSearch search
            , HttpServletResponse response) throws IOException {

        studentBaseInfoService.exportDelaySchoolStudent(search, response);
    }
}
