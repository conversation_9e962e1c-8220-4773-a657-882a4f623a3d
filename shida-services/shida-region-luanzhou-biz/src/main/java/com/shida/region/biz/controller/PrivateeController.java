package com.shida.region.biz.controller;

import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.annotation.SaMode;
import com.shida.constant.RoleConstant;
import com.shida.noRepeatSubmit.NoRepeatSubmit;
import com.shida.region.biz.pojo.from.EnrollOrgForm;
import com.shida.region.biz.service.IPrivateeStudentService;
import com.shida.region.biz.service.IPrivateeTimeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @Date 2025/3/31 9:27
 * @PackageName:com.shida.region.biz.controller
 * @ClassName: PrivateeController
 * @Version 1.0
 */
@Tag(name = "民办管理")
@RestController
@RequestMapping("/privatee/biz/service")
public class PrivateeController {


    @Resource
    private IPrivateeStudentService privateeStudentService;

    @PostMapping("organizationSubmitEnrollInfo")
    @SaCheckRole(value = {RoleConstant.SCHOOL_CODE,RoleConstant.COUNTY_ADMIN_CODE},mode = SaMode.OR)
    @Operation(summary = "学校教育局提交报名信息")
    @NoRepeatSubmit(value = 10)
    public String organizationSubmitEnrollInfo(@RequestBody @Valid EnrollOrgForm form){
        form.setClientId("pc");
        return privateeStudentService.organizationSubmitEnrollInfo(form);
    }






}
