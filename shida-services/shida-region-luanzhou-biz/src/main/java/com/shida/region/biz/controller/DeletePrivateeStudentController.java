package com.shida.region.biz.controller;

import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.annotation.SaMode;
import com.shida.autolog.AutoLog;
import com.shida.autolog.LogType;
import com.shida.autolog.OperateType;
import com.shida.constant.RoleConstant;
import com.shida.noRepeatSubmit.NoRepeatSubmit;
import com.shida.region.biz.pojo.excel.ExcelBatchImportResponse;
import com.shida.region.biz.pojo.from.DeleteStudentForm;
import com.shida.region.biz.pojo.from.RecoverClearEnrollStuInfoForm;
import com.shida.region.biz.service.IDeletePrivateeStudentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/10 16:26
 * @PackageName:com.shida.region.biz.controller
 * @ClassName: DeletePrivateeStudentController
 * @Version 1.0
 */
@RestController
@RequestMapping("/privatee/biz/enrollment/")
@Tag(name = "清除民办学生报名")
public class DeletePrivateeStudentController {

    @Resource
    private IDeletePrivateeStudentService deletePrivateeStudentService;


    @PostMapping("deleteEnrollInfo")
    @SaCheckRole(value = {RoleConstant.SUPER_ADMIN_CODE,RoleConstant.CITY_ADMIN_CODE,RoleConstant.COUNTY_ADMIN_CODE}, mode = SaMode.OR)
    @Operation(summary = "清除学生报名")
    @AutoLog(logType = LogType.OPERATE,operateType = OperateType.OPERATE_DELETE)
    public Boolean deleteEnrollInfo(@RequestBody DeleteStudentForm form){
        return deletePrivateeStudentService.clearEnrollInfo(form);
    }

    @PostMapping("exportBatchClearStuExcel")
    @SaCheckRole(value = {RoleConstant.SUPER_ADMIN_CODE,RoleConstant.CITY_ADMIN_CODE,RoleConstant.COUNTY_ADMIN_CODE}, mode = SaMode.OR)
    @Operation(summary = "导出批量学生报名数据模板下载")
    public void exportBatchClearStuExcel(HttpServletResponse response){
        deletePrivateeStudentService.exportBatchClearStuExcel(response);
    }

    @PostMapping("importBatchClearStuExcel")
    @SaCheckRole(value = {RoleConstant.SUPER_ADMIN_CODE,RoleConstant.CITY_ADMIN_CODE,RoleConstant.COUNTY_ADMIN_CODE}, mode = SaMode.OR)
    @Operation(summary = "导入批量学生报名数据模板")
    @AutoLog(logType = LogType.OPERATE,operateType = OperateType.OPERATE_DELETE)
    public List<ExcelBatchImportResponse> importBatchClearStuExcel(@RequestParam("file") MultipartFile file) throws IOException {
        return deletePrivateeStudentService.importBatchClearStuExcel(file);
    }

    @PostMapping("recoverEnrollInfo")
    @SaCheckRole(value = {RoleConstant.SUPER_ADMIN_CODE,RoleConstant.CITY_ADMIN_CODE,RoleConstant.COUNTY_ADMIN_CODE}, mode = SaMode.OR)
    @Operation(summary = "恢复清除的学生报名信息")
    @NoRepeatSubmit(value = 10)
    @AutoLog(logType = LogType.OPERATE,operateType = OperateType.OPERATE_EDIT)
    public Boolean recoverEnrollInfo(@RequestBody @Valid RecoverClearEnrollStuInfoForm form){
        return deletePrivateeStudentService.recoverEnrollInfo(form);
    }


}
