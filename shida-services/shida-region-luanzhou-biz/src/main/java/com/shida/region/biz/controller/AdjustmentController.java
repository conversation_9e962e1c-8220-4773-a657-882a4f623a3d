package com.shida.region.biz.controller;


import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.annotation.SaMode;

import com.shida.autolog.AutoLog;
import com.shida.autolog.OperateType;
import com.shida.constant.RoleConstant;

import com.shida.region.biz.pojo.from.*;

import com.shida.region.biz.service.IAdjustmentService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;


/**
 * <AUTHOR>
 * @Date 2024/4/18 17:37
 */
@Tag(name = "市局调剂")
@RestController
@RequestMapping("/biz/cityAdjustment")
public class AdjustmentController {

    @Resource
    private IAdjustmentService adjustmentService;



    @SaCheckRole(value = {RoleConstant.CITY_ADMIN_CODE}, mode = SaMode.OR)
    @AutoLog(operateType = OperateType.OPERATE_ADD)
    @Operation(summary = "调剂学生")
    @PostMapping("/adjustment")
    public void AdjustmentStudent(@RequestBody @Valid AdjustmentForm form) {
        adjustmentService.AdjustmentStudent(form);
    }



}
