package com.shida.region.biz.controller;

import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.annotation.SaMode;
import cn.hutool.core.util.StrUtil;
import com.shida.autolog.AutoLog;
import com.shida.autolog.LogType;
import com.shida.autolog.OperateType;
import com.shida.constant.RoleConstant;
import com.shida.enums.RestCode;
import com.shida.exceptions.ServiceException;
import com.shida.noRepeatSubmit.NoRepeatSubmit;
import com.shida.pojo.form.KeyForm;
import com.shida.region.biz.enums.PrivateeTimeTypeCode;
import com.shida.region.biz.pojo.from.EnrollForm;
import com.shida.region.biz.pojo.from.EnrollQuerySearchForm;
import com.shida.region.biz.pojo.from.ModifyGetFieldConfigForm;
import com.shida.region.biz.pojo.from.StuBindingWeChatForm;
import com.shida.region.biz.pojo.vo.EnrollQuerySearchTypeVo;
import com.shida.region.biz.pojo.vo.EnrollQueryVo;
import com.shida.region.biz.pojo.vo.ModifyEnrollInfoVo;
import com.shida.region.biz.pojo.vo.PrivateeTimeInfo;
import com.shida.region.biz.service.IPrivateeStudentService;
import com.shida.region.biz.service.IPrivateeTimeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2025/3/31 9:28
 * @PackageName:com.shida.region.biz.controller
 * @ClassName: ParentPrivateeController
 * @Version 1.0
 */
@Tag(name = "民办家长报名")
@RestController
@RequestMapping("/privatee/biz/parent")
public class ParentPrivateeController {

    @Resource
    private IPrivateeStudentService privateeStudentService;
    @Resource
    private IPrivateeTimeService privateeTimeService;

    @PostMapping("getPrivateeStatus")
    @SaCheckRole(RoleConstant.PARENT_CODE)
    @Operation(summary = "民办报名入口是否开启")
    public Boolean getPrivateeStatus(){
        return privateeStudentService.getPrivateeStatus();
    }

    @PostMapping("checkPrivateeSignTime")
    @SaCheckRole(RoleConstant.PARENT_CODE)
    @Operation(summary = "民办报名入口时间校验")
    public Boolean checkPrivateeSignTime(@RequestBody @Valid KeyForm<Long> form){
        Long primaryTimeId = 1004L;
        Long juniorTimeId = 1005L;
        String timeKey = null;
        if (primaryTimeId.equals(form.getKey())) {
            timeKey = PrivateeTimeTypeCode.PRIMARY_SIGN.getValue();
        }else if (juniorTimeId.equals(form.getKey())) {
            timeKey = PrivateeTimeTypeCode.JUNIOR_SIGN.getValue();
        }else{
            throw new SecurityException("报名入口不存在");
        }
        PrivateeTimeInfo timeInfo = privateeTimeService.getPrivateeTimeInfoBy(timeKey);
        LocalDateTime now = LocalDateTime.now();
        if (now.isBefore(timeInfo.getBeginTime())){
            throw new SecurityException("报名时间还未开启，请耐心等待");
        }
        if (now.isAfter(timeInfo.getEndTime())) {
            throw new SecurityException("报名时间已结束，请知悉");
        }
        if (now.isAfter(timeInfo.getBeginTime()) && now.isBefore(timeInfo.getEndTime())) {
            return true;
        }else{
            return false;
        }
    }

    @PostMapping("submitEnrollInfo")
    @SaCheckRole(RoleConstant.PARENT_CODE)
    @Operation(summary = "提交报名信息")
    @NoRepeatSubmit(value = 10)
    @AutoLog(logType = LogType.OPERATE,operateType = OperateType.OPERATE_ADD)
    public String submitEnrollInfo(@RequestBody @Valid EnrollForm form, @RequestHeader("client-id") String clientId){
        if(StrUtil.isEmpty(clientId)){
            throw new ServiceException(RestCode.DATA_ERROR,"报名端错误");
        }
        form.setClientId(clientId);
        return privateeStudentService.submitEnrollInfo(form);
    }


    @PostMapping("rejectOrModifyEnrollInfo")
    @Operation(summary = "驳回或修改后提交报名信息")
    @SaCheckRole(RoleConstant.PARENT_CODE)
    @NoRepeatSubmit(value = 10)
    @AutoLog(logType = LogType.OPERATE,operateType = OperateType.OPERATE_EDIT)
    public String rejectOrModifyEnrollInfo(@RequestBody EnrollForm form,@RequestHeader("client-id") String clientId){
        if(StrUtil.isEmpty(clientId)){
            throw new ServiceException(RestCode.DATA_ERROR,"报名端错误");
        }
        form.setClientId(clientId);
        return privateeStudentService.rejectOrModifyEnrollInfo(form);
    }

    @SaCheckRole(value = {RoleConstant.PARENT_CODE}, mode = SaMode.OR)
    @Operation(summary = "是否已经报名")
    @PostMapping("/checkSign")
    public Boolean checkSign() {
        return privateeStudentService.checkSign();
    }



    @SaCheckRole(value = {RoleConstant.PARENT_CODE}, mode = SaMode.OR)
    @Operation(summary = "查看是否延迟上学申请报名")
    @PostMapping("/delayCheckSign")
    public Boolean delayCheckSign() {
        return privateeStudentService.delayCheckSign();
    }

    @SaCheckRole(value = {RoleConstant.PARENT_CODE}, mode = SaMode.OR)
    @Operation(summary = "查看是否市外就读报名")
    @PostMapping("/outSideCheckSign")
    public Boolean outSideCheckSign(@RequestBody @Valid KeyForm<Integer> form) {
        return privateeStudentService.outSideCheckSign(form);
    }


    @SaCheckRole(value = {RoleConstant.PARENT_CODE}, mode = SaMode.OR)
    @Operation(summary = "是否民办报名")
    @PostMapping("/checkPrivateeStatus")
    public Boolean checkPrivateeStatus() {
        return privateeStudentService.checkPrivateeStatus();
    }

    @SaCheckRole(value = {RoleConstant.PARENT_CODE}, mode = SaMode.OR)
    @Operation(summary = "录取查询")
    @PostMapping("/onePage")
    public EnrollQueryVo enrollQueryOnePage() {
        return privateeStudentService.enrollQueryResult();
    }


    @AutoLog(logType = LogType.OPERATE,operateType = OperateType.OPERATE_DELETE)
    @SaCheckRole(value = {RoleConstant.PARENT_CODE}, mode = SaMode.OR)
    @Operation(summary = "家长-自主删除")
    @PostMapping("/delete")
    public Boolean selfDelete() {
        return privateeStudentService.selfDelete();
    }
}
