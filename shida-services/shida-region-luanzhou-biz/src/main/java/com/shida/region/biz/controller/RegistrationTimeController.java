package com.shida.region.biz.controller;


import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.annotation.SaMode;
import com.shida.autolog.AutoLog;
import com.shida.autolog.OperateType;
import com.shida.bizCenter.dto.HomePageRegistrationTimeVerifyVo;
import com.shida.constant.RoleConstant;
import com.shida.noRepeatSubmit.NoRepeatSubmit;
import com.shida.pojo.form.KeyForm;
import com.shida.region.biz.pojo.from.RegistrationTimeForm;
import com.shida.region.biz.pojo.vo.RegistrationTimeVo;
import com.shida.region.biz.service.IRegistrationTimeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/4/18 17:37
 */
@Tag(name = "教育局-报名时间设置")
@RestController
@RequestMapping("/biz/registrationTime")
public class RegistrationTimeController {

    @Resource
    private IRegistrationTimeService registrationTimeService;

    @AutoLog(operateType = OperateType.OPERATE_ADD)
    @SaCheckRole(value = {RoleConstant.COUNTY_ADMIN_CODE}, mode = SaMode.OR)
    @Operation(summary = "新增报名时间设置")
    @PostMapping("create")
    @NoRepeatSubmit
    public Boolean create(@RequestBody @Valid RegistrationTimeForm form) {
        return registrationTimeService.create(form);
    }

    @AutoLog(operateType = OperateType.OPERATE_ADD)
    @SaCheckRole(value = {RoleConstant.COUNTY_ADMIN_CODE}, mode = SaMode.OR)
    @Operation(summary = "新增截止报名")
    @PostMapping("deadlineCreate")
    @NoRepeatSubmit
    public Boolean deadlineCreate(@RequestBody @Valid RegistrationTimeForm form) {
        return registrationTimeService.deadlineCreate(form);
    }

    @Operation(summary = "报名端时间校验")
    @SaCheckRole(value = {RoleConstant.COUNTY_ADMIN_CODE,RoleConstant.PARENT_CODE,RoleConstant.SCHOOL_CODE}, mode = SaMode.OR)
    @PostMapping("homePageVerify")
    public HomePageRegistrationTimeVerifyVo homePageVerify() {
        return registrationTimeService.getRegistraionAllVerify();
    }

    @Operation(summary = "根据setUpId校验时间")
    @SaCheckRole(value = {RoleConstant.COUNTY_ADMIN_CODE,RoleConstant.PARENT_CODE,RoleConstant.SCHOOL_CODE}, mode = SaMode.OR)
    @PostMapping("getVerifyBySetUpId")
    public Boolean getVerifyBySetUpId(@RequestBody @Valid KeyForm<Long> setUpId) {
        return registrationTimeService.getVerifyBySetUpId(setUpId);
    }

    @Operation(summary = "单个报名时间校验:参数type 返回值：true:在报名时间内 false：报名时间 前/后")
    @SaCheckRole(value = {RoleConstant.COUNTY_ADMIN_CODE}, mode = SaMode.OR)
    @PostMapping("verify")
    public Boolean verify(@RequestBody @Valid KeyForm<String> form) {
        return registrationTimeService.getVerifyByType(form.getKey());
    }

    @Operation(summary = "回显报名时间设置")
    @SaCheckRole(value = {RoleConstant.COUNTY_ADMIN_CODE}, mode = SaMode.OR)
    @PostMapping("list")
    public List<RegistrationTimeVo> list(@RequestBody @Valid KeyForm<String> type) {
        return registrationTimeService.getInfoByType(type.getKey());
    }

    @Operation(summary = "回显学校截止时间")
    @SaCheckRole(value = {RoleConstant.COUNTY_ADMIN_CODE}, mode = SaMode.OR)
    @PostMapping("deadline")
    public List<RegistrationTimeVo> deadline() {
        return registrationTimeService.getDeadline();
    }

    @Operation(summary = "校验学校截止添加报名时间")
    @SaCheckRole(value = {RoleConstant.SCHOOL_CODE}, mode = SaMode.OR)
    @PostMapping("verifyDeadline")
    public Boolean verifyDeadline(@RequestBody @Valid KeyForm<Integer> form) {
        return registrationTimeService.verifyDeadline(form.getKey());
    }
}
