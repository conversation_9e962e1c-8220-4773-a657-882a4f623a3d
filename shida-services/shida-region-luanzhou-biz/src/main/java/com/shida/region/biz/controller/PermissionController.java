package com.shida.region.biz.controller;

import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.annotation.SaMode;
import com.shida.autolog.AutoLog;
import com.shida.autolog.OperateType;
import com.shida.constant.RoleConstant;
import com.shida.pojo.form.KeyForm;
import com.shida.region.biz.pojo.from.SysPermissionForm;
import com.shida.region.biz.pojo.vo.SysPermissionInfo;
import com.shida.region.biz.service.ISysPermissionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * Description:
 *
 * <AUTHOR>
 * @Create 2025/1/7 9:49
 */
@Tag(name = "功能开关")
@RestController
@RequestMapping("/biz/permission")
public class PermissionController {
    @Resource
    private ISysPermissionService permissionService;

    @SaCheckRole(value = {RoleConstant.CITY_ADMIN_CODE}, mode = SaMode.OR)
    @Operation(summary = "查询-功能开关设置")
    @PostMapping("list")
    public List<SysPermissionInfo> getPermissionList(){
        return permissionService.getPermissionList();
    }

    @AutoLog(operateType = OperateType.OPERATE_ADD)
    @SaCheckRole(value = {RoleConstant.CITY_ADMIN_CODE}, mode = SaMode.OR)
    @Operation(summary = "保存-功能开关设置")
    @PostMapping("save")
    public Boolean savePermission(@RequestBody SysPermissionForm form){
        return permissionService.updateSysPermission(form);
    }

    @SaCheckRole(value = {RoleConstant.CITY_ADMIN_CODE,RoleConstant.COUNTY_ADMIN_CODE,RoleConstant.SCHOOL_CODE,RoleConstant.PARENT_CODE,RoleConstant.AUDITOR_CODE}, mode = SaMode.OR)
    @Operation(summary = "查询-功能开关设置")
    @PostMapping("getPermissionStatus")
    public Boolean getPermissionStatus(@RequestBody @Valid KeyForm<String> form){
        return permissionService.getPermissionStatus(form.getKey());
    }
}
