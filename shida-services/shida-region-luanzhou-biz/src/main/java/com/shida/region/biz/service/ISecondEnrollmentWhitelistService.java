package com.shida.region.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.shida.pojo.search.Search;
import com.shida.pojo.vo.PageInfo;
import com.shida.region.biz.entity.SecondEnrollmentWhitelist;
import com.shida.region.biz.pojo.from.SecondEnrollmentWhitelistForm;
import com.shida.region.biz.pojo.vo.SecondEnrollmentWhitelistVo;

/**
 * <p>
 * 二次报名白名单表 服务类
 * </p>
 *
 * <AUTHOR> @since 2024-04-30
 */
public interface ISecondEnrollmentWhitelistService extends IService<SecondEnrollmentWhitelist> {


    /**
     * 分页查询白名单列表
     * @param search 白名单信息
     * @return 白名单记录
     */
    PageInfo<SecondEnrollmentWhitelistVo> pageList(Search search);
    /**
     * 查询白名单信息
     * @param whitelist 白名单对象
     * @return 查询白名单
     */
    Boolean createWhitelist(SecondEnrollmentWhitelistForm whitelist);

    /**
     * 更改白名单的信息
     * @param whitelist 白名单对象
     * @return 更改白名单
     */
    Boolean updateWhitelist(SecondEnrollmentWhitelistForm whitelist);

    /**
     * 删除白名单信息
     * @param id 对象id
     * @return 删除白名单
     */
    boolean deleteWhitelist(Long id);
} 