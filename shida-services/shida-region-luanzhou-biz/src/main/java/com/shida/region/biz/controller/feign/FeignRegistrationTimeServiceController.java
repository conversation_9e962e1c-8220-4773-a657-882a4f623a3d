package com.shida.region.biz.controller.feign;


import com.shida.bizCenter.api.IFeignRegistrationTimeService;
import com.shida.bizCenter.dto.HomePageRegistrationTimeVerifyVo;
import com.shida.pojo.form.KeyForm;

import com.shida.region.biz.service.IRegistrationTimeService;

import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


@Tag(name = "Feign-biz服务")
@RestController
@Slf4j
public class FeignRegistrationTimeServiceController implements IFeignRegistrationTimeService {

    @Resource
    private IRegistrationTimeService registrationTimeService;

    @Override
    public Boolean verify(String token, KeyForm form) {
        log.info("openFeign请求头参数=============" + token);
        // true:在报名时间内  false：报名时间 前/后
       return registrationTimeService.getVerifyByType(form.getKey().toString());
    }

    @Override
    public HomePageRegistrationTimeVerifyVo getRegistraionAllVerify(String token) {
        log.info("openFeign请求头参数=============" + token);
        // true:在报名时间内  false：报名时间 前/后
        return registrationTimeService.getRegistraionAllVerify();
    }


}
