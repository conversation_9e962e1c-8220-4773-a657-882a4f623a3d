package com.shida.region.biz.controller;


import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.annotation.SaMode;

import com.shida.autolog.AutoLog;
import com.shida.autolog.OperateType;
import com.shida.constant.RoleConstant;
import com.shida.noRepeatSubmit.NoRepeatSubmit;
import com.shida.pojo.form.KeyForm;
import com.shida.pojo.vo.PageInfo;
import com.shida.region.biz.pojo.from.StuPhaseForm;

import com.shida.region.biz.pojo.search.StuPhaseSearch;
import com.shida.region.biz.pojo.vo.StuPhaseInfo;
import com.shida.region.biz.pojo.vo.StuPhaseVo;
import com.shida.region.biz.service.IStuPhaseService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @Date 2024/4/18 17:37
 */
@Tag(name = "学段设置")
@RestController
@RequestMapping("/biz/stuPhase")
public class StuPhaseController {

    @Resource
    private IStuPhaseService stuPhaseService;

    @PostMapping("getSystemName")
    @Operation(summary = "查询系统名称")
    public String getSystemName(){
        return stuPhaseService.getSystemName();
    }

    @SaCheckRole(value = {RoleConstant.SUPER_ADMIN_CODE}, mode = SaMode.OR)
    @Operation(summary = "学段设置分页")
    @PostMapping("page")
    public PageInfo<StuPhaseVo> page(@RequestBody StuPhaseSearch search) {
        return stuPhaseService.getPageData(search);
    }

    @Operation(summary = "新增学段设置")
    @AutoLog(operateType = OperateType.OPERATE_ADD)
    @SaCheckRole(value = {RoleConstant.SUPER_ADMIN_CODE}, mode = SaMode.OR)
    @PostMapping("create")
    @NoRepeatSubmit
    public Boolean create(@RequestBody @Valid StuPhaseForm form) {
        return stuPhaseService.create(form);
    }

    @Operation(summary = "公示录取结果: (参数 1:小学 2初中)")
    @AutoLog(operateType = OperateType.OPERATE_EDIT)
    @SaCheckRole(value = {RoleConstant.CITY_ADMIN_CODE}, mode = SaMode.OR)
    @PostMapping("publicityResult")
    @NoRepeatSubmit
    public Boolean publicityResult(@RequestBody @Valid KeyForm<Integer> keyForm) {
        return stuPhaseService.publicityResult(keyForm.getKey());
    }

    @Operation(summary = "编辑学段设置")
    @PostMapping("edit")
    @AutoLog(operateType = OperateType.OPERATE_EDIT)
    @SaCheckRole(value = {RoleConstant.SUPER_ADMIN_CODE}, mode = SaMode.OR)
    @NoRepeatSubmit
    public Boolean edit(@RequestBody @Valid StuPhaseForm form) {
        return stuPhaseService.edit(form);
    }

    @Operation(summary = "回显学段设置info")
    @PostMapping("/info")
    @SaCheckRole(value = {RoleConstant.SUPER_ADMIN_CODE}, mode = SaMode.OR)
    public StuPhaseInfo info(@RequestBody @Valid KeyForm<Long> form) {
        return stuPhaseService.info(form.getKey());
    }


    @Operation(summary = "download")
    @PostMapping("/download")
    @SaCheckRole(value = {RoleConstant.SUPER_ADMIN_CODE}, mode = SaMode.OR)
    @AutoLog(operateType = OperateType.OPERATE_IMPORT)
    public void download(HttpServletResponse response) {
        stuPhaseService.excel(response);
    }

    @PostMapping("upload")
    @Operation(summary = "upload")
    @SaCheckRole(value = {RoleConstant.SUPER_ADMIN_CODE}, mode = SaMode.OR)
    @AutoLog(operateType = OperateType.OPERATE_IMPORT)
    public String upload(MultipartFile file)  {
        stuPhaseService.upload(file);
        return "success";
    }

}
