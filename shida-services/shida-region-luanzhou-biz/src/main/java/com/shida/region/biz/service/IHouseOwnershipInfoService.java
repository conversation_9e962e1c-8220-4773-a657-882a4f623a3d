package com.shida.region.biz.service;

import com.shida.pojo.vo.PageInfo;
import com.shida.region.biz.entity.HouseOwnershipInfo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.shida.region.biz.pojo.from.SecurityAndHouseNoPassForm;
import com.shida.region.biz.pojo.search.PublicSecurityAndHouseSearch;
import com.shida.region.biz.pojo.vo.PublicSecurityAndHousePropertyVo;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;

/**
 * <p>
 * 学生报名房屋所有权证信息表 服务类
 * </p>
 *
 * <AUTHOR> @since 2024-04-30
 */
public interface IHouseOwnershipInfoService extends IService<HouseOwnershipInfo> {

    PageInfo<PublicSecurityAndHousePropertyVo> getPublicSecurityAndHousePropertyPage(@Valid PublicSecurityAndHouseSearch search);

    PageInfo<PublicSecurityAndHousePropertyVo> getHukouAndJuzhuPage(@Valid PublicSecurityAndHouseSearch search);

    void exportHouseProperty(@Valid PublicSecurityAndHouseSearch search, HttpServletResponse response) throws IOException;

    void exportPoliceInformation(@Valid PublicSecurityAndHouseSearch search, HttpServletResponse response) throws IOException;

    void importPoliceInformation(MultipartFile file, Integer publicSecurityReviewType);

    void passSecurityAndHouseAudit(@Valid SecurityAndHouseNoPassForm form);

    void notPassSecurityAndHouseAudit(@Valid SecurityAndHouseNoPassForm form);

    void importHouseProperty(MultipartFile file);
}
