package com.shida.region.biz.controller;

import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.annotation.SaMode;
import com.shida.autolog.AutoLog;
import com.shida.autolog.OperateType;
import com.shida.constant.RoleConstant;
import com.shida.pojo.form.KeyForm;
import com.shida.region.biz.pojo.from.PrivateeTimeForm;
import com.shida.region.biz.pojo.vo.PrivateeTimeInfo;
import com.shida.region.biz.service.IPrivateeTimeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @Date 2025/4/1 16:10
 * @PackageName:com.shida.region.biz.controller.task
 * @ClassName: PrivateeTimeController
 * @Version 1.0
 */
@Tag(name = "民办报名时间设置")
@RestController
@RequestMapping("/privatee/biz/time")
public class PrivateeTimeController {

    @Resource
    private IPrivateeTimeService privateeTimeService;

    @Operation(summary = "报名时间详情")
    @PostMapping("detail")
    public PrivateeTimeInfo getDetail(@RequestBody @Valid KeyForm<Long> form){
        return privateeTimeService.getDetail(form.getKey());
    }

    @AutoLog(operateType = OperateType.OPERATE_EDIT)
    @SaCheckRole(value = {RoleConstant.COUNTY_ADMIN_CODE}, mode = SaMode.OR)
    @Operation(summary = "修改报名时间")
    @PostMapping("update")
    public Boolean updateInfo(@RequestBody @Valid PrivateeTimeForm form) {
        return privateeTimeService.updateInfo(form);
    }

    @SaCheckRole(value = {RoleConstant.COUNTY_ADMIN_CODE,RoleConstant.SCHOOL_CODE,RoleConstant.PARENT_CODE}, mode = SaMode.OR)
    @Operation(summary = "检查")
    @PostMapping("check")
    public Boolean checkTime(@RequestBody @Valid KeyForm<String> form){
        return privateeTimeService.checkTime(form.getKey());
    }
}
