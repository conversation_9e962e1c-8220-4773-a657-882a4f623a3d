package com.shida.region.biz.controller;

import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.annotation.SaMode;
import com.shida.constant.RoleConstant;
import com.shida.pojo.form.KeyForm;
import com.shida.region.biz.entity.InfoExcelName;
import com.shida.region.biz.pojo.from.ExcelNameForm;
import com.shida.region.biz.pojo.from.SaveExcelConfigForm;
import com.shida.region.biz.pojo.search.PublicSecurityAndHouseSearch;
import com.shida.region.biz.pojo.search.RecruitStudentSearch;
import com.shida.region.biz.pojo.vo.ExcelMiddleConfigVo;
import com.shida.region.biz.service.IInfoExcelNameService;
import com.shida.region.biz.service.IInfoSetExcelService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
 * Description:
 *
 * <AUTHOR>
 * @Create 2024/11/12 9:39
 */
@RestController
@RequestMapping("/biz/excelConfig/")
@Tag(name = "Excel配置导出")
public class ExcelConfigurationController {

    @Resource
    private IInfoSetExcelService infoSetExcelService;
    @Resource
    private IInfoExcelNameService infoExcelNameService;

    @PostMapping("getExcelNameList")
    @Operation(summary = "获取报名表格配置名称")
    @SaCheckRole(RoleConstant.SUPER_ADMIN_CODE)
    public List<InfoExcelName> getExcelNameList() {
        return infoExcelNameService.getExcelNameList();
    }

    @PostMapping("setExcelName")
    @Operation(summary = "设置表名称")
    @SaCheckRole(RoleConstant.SUPER_ADMIN_CODE)
    public String setExcelName(@RequestBody @Valid ExcelNameForm form) {
        return infoExcelNameService.setExcelName(form);
    }

    @PostMapping("getExcelConfigInfo")
    @Operation(summary = "获取excel配置字段")
    @SaCheckRole(RoleConstant.SUPER_ADMIN_CODE)
    public List<ExcelMiddleConfigVo> getExcelConfigInfo(@RequestBody @Valid KeyForm<Integer> form) {
        return infoSetExcelService.getExcelConfigInfo(form.getKey());
    }

    @PostMapping("saveExcelConfigInfo")
    @Operation(summary = "保存excel配置字段")
    @SaCheckRole(RoleConstant.SUPER_ADMIN_CODE)
    public String saveExcelConfigInfo(@RequestBody @Valid SaveExcelConfigForm form) {
        return infoSetExcelService.saveExcelConfigInfo(form);
    }

    @PostMapping("exportExcelPriMiddle")
    @Operation(summary = "导出excel小学初中")
    @SaCheckRole(value = {RoleConstant.COUNTY_ADMIN_CODE, RoleConstant.SCHOOL_CODE}, mode = SaMode.OR)
    public void exportExcelPriMiddle(@RequestBody @Valid RecruitStudentSearch search, HttpServletResponse response) throws IOException {
        infoSetExcelService.exportExcelPriMiddle(search, response);
    }

    @PostMapping("exportExcelPriMiddleS")
    @Operation(summary = "导出外地上学申请管理")
    @SaCheckRole(value = {RoleConstant.COUNTY_ADMIN_CODE, RoleConstant.SCHOOL_CODE}, mode = SaMode.OR)
    public void exportExcelPriMiddleS(@RequestBody @Valid RecruitStudentSearch search, HttpServletResponse response) throws IOException {
        infoSetExcelService.exportExcelPriMiddleS(search, response);
    }
    @PostMapping("exportExcelPublicHouse")
    @Operation(summary = "导出excel公安房管")
    @SaCheckRole(RoleConstant.COUNTY_ADMIN_CODE)
    public void exportExcelPublicHouse(@RequestBody @Valid PublicSecurityAndHouseSearch search, HttpServletResponse response) throws IOException {
        infoSetExcelService.exportExcelPublicHouse(search, response);
    }

}
