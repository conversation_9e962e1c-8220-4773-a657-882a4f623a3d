package com.shida.region.biz.controller;

import cn.dev33.satoken.annotation.SaCheckRole;
import com.shida.autolog.AutoLog;
import com.shida.autolog.LogType;
import com.shida.autolog.OperateType;
import com.shida.constant.RoleConstant;
import com.shida.noRepeatSubmit.NoRepeatSubmit;
import com.shida.pojo.form.KeyForm;
import com.shida.region.biz.pojo.from.*;
import com.shida.region.biz.pojo.search.FieldConfigInfoSearch;
import com.shida.region.biz.pojo.vo.ConfigIsShowVo;
import com.shida.region.biz.pojo.vo.FieldMiddleConfigInfoVo;
import com.shida.region.biz.pojo.vo.signUpSortDetailSettingVo;
import com.shida.region.biz.service.IInfoRegistrationConfigShowService;
import com.shida.region.biz.service.IInfoSetFieldService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * Description:
 *
 * @Author: laobaduo
 * @Create: 2024/4/22 - 10:44
 */
@RestController
@RequestMapping("/biz/fieldConfig/")
@Tag(name = "字段配置")
public class FieldConfigController {

    @Resource
    private IInfoSetFieldService infoSetFieldService;
    @Resource
    private IInfoRegistrationConfigShowService infoRegistrationConfigShowService;


    @SaCheckRole(value = {RoleConstant.SUPER_ADMIN_CODE})
    @PostMapping("getRootConfigIsShow")
    @AutoLog(logType = LogType.OPERATE,operateType = OperateType.OPERATE_SEARCH)
    @Operation(summary = "查询根目录显示情况")
    public List<ConfigIsShowVo> getRootConfigIsShow(@RequestBody @Valid KeyForm<Long> form){
        return infoRegistrationConfigShowService.getRootConfigIsShow(form);
    }

    @SaCheckRole(value = {RoleConstant.SUPER_ADMIN_CODE})
    @PostMapping("setRootConfigIsShow")
    @AutoLog(logType = LogType.OPERATE,operateType = OperateType.OPERATE_EDIT)
    @Operation(summary = "设置根目录显示情况")
    public boolean setRootConfigIsShow(@RequestBody @Valid SetRootConfigIsShow form){
        return infoRegistrationConfigShowService.setRootConfigIsShow(form);
    }

    @PostMapping("signUpSortDetailSettingUpdate")
    @Operation(summary = "报名类别详情设置编辑")
    @AutoLog(logType = LogType.OPERATE,operateType = OperateType.OPERATE_EDIT)
    @NoRepeatSubmit
    public boolean signUpSortDetailSettingUpdate(@RequestBody @Valid signUpSortDetailSettingForm form){
        return infoSetFieldService.signUpSortDetailSettingUpdate(form);
    }

    @SaCheckRole(value = {RoleConstant.SUPER_ADMIN_CODE})
    @PostMapping("getFieldConfigInfo")
    @Operation(summary = "获取中间配置字段信息")
    @AutoLog(logType = LogType.OPERATE,operateType = OperateType.OPERATE_SEARCH)
    public List<FieldMiddleConfigInfoVo> getFieldConfigInfo(@RequestBody @Valid FieldConfigInfoSearch search){
        return infoSetFieldService.getFieldConfigInfo(search);
    }

    @SaCheckRole(value = {RoleConstant.SUPER_ADMIN_CODE})
    @PostMapping("setFieldConfigInfo")
    @NoRepeatSubmit
    @Operation(summary = "保存中间配置字段信息")
    @AutoLog(logType = LogType.OPERATE,operateType = OperateType.OPERATE_EDIT)
    public boolean setFieldConfigInfo(@RequestBody @Valid FieldMiddleConfigSaveForm form){
        return infoSetFieldService.setFieldConfigInfo(form);
    }

    @SaCheckRole(value = {RoleConstant.SUPER_ADMIN_CODE})
    @PostMapping("setFieldName")
    @Operation(summary = "修改字段中文名")
    @AutoLog(logType = LogType.OPERATE,operateType = OperateType.OPERATE_SEARCH)
    @NoRepeatSubmit
    public boolean setFieldName(@RequestBody @Valid SetFieldNameForm form){
        return infoSetFieldService.setFieldName(form);
    }

    @PostMapping("signUpSortDetailSetting")
    @Operation(summary = "报名类别详情设置")
    @AutoLog(logType = LogType.OPERATE,operateType = OperateType.OPERATE_SEARCH)
    @NoRepeatSubmit
    public List<signUpSortDetailSettingVo> signUpSortDetailSetting(@RequestBody @Valid KeyForm<Long> form){
        return infoSetFieldService.signUpSortDetailSetting(form.getKey());
    }

    @PostMapping("syncEnrollField")
    @Operation(summary = "同步报名字段")
    @AutoLog(logType = LogType.OPERATE,operateType = OperateType.OPERATE_EDIT)
    public String syncEnrollField(@RequestBody @Valid SyncEnrollFieldForm form){
        return infoSetFieldService.syncEnrollField(form);
    }


}
