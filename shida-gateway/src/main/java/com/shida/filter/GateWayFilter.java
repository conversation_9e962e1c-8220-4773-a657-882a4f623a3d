package com.shida.filter;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.xiaoymin.knife4j.spring.gateway.utils.StrUtil;
import com.shida.config.KeyConstant;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @Date 2024/6/26 19:29
 * @PackageName:com.shida.filter
 * @ClassName: GateWayFilter
 * @Version 1.0
 */
@Component
@Slf4j
public class GateWayFilter implements GlobalFilter, Ordered {

    @Resource
    private RedisTemplate redisTemplate;
    @Value("#{'${white-path.list}'.split(' ')}")
    private List<String> whitePath;

    @Value("${spring.profiles.active}")
    private String dev;

    @SneakyThrows
    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        if (dev.equals("dev2")) {
            return chain.filter(exchange);
        } else {
            //        log.info(exchange.getRequest().getPath().toString());
            MultiValueMap<String, String> headers = exchange.getRequest().getHeaders();
            ServerHttpResponse response = exchange.getResponse();
//        for(String path:whitePath ){
//            System.out.println(path);
//        }
            String path = exchange.getRequest().getPath().toString();
            String service = path.substring(0, path.indexOf("/", 1));
            String imgUrl = path.substring(path.indexOf("/", 2), path.length());


            Boolean whiteFlag = false;
            if (imgUrl.startsWith("/upload")) {
                whiteFlag = true;
            }
            //swagger接口文档
//        if (imgUrl.startsWith("/v3")) {
//            whiteFlag = true;
//        }
            if (whitePath.contains(service)) {
                whiteFlag = true;
            }
            if (whitePath.contains(path)) {
                whiteFlag = true;
            }
            if (whiteFlag) {
                // 放行
                return chain.filter(exchange);
            } else {
                if (headers.containsKey("client-id")) {
                    if (headers.containsKey("Authorization")) {
                        if (!headers.containsKey("state")) {
                            response.getHeaders().add("Content-Type", "application/json;charset=UTF-8");
                            return response.writeWith(Mono.just(getError(response)));
                        }
//                    log.info("token:===============" + headers.getFirst("Authorization"));
                        String authorization = headers.getFirst("Authorization");
                        if (StrUtil.isBlank(authorization)  ) {
                            return chain.filter(exchange);
                        }else{
                            if (authorization.equals("Bearer")) {
                                return chain.filter(exchange);
                            }
                        }
                        String token = authorization.split(" ")[0];
                        String state = headers.getFirst("state");
//                    String key = KeyConstant.USER_STATE +  token;
//                    Boolean has = redisTemplate.hasKey(key);
                        if (redisTemplate.hasKey(KeyConstant.USER_STATE + token)) {
                            String dbState = (String) redisTemplate.opsForValue().get(KeyConstant.USER_STATE + token);
                            if (state.equals(dbState)) {
                                // 放行
                                return chain.filter(exchange);
                            } else {
                                response.getHeaders().add("Content-Type", "application/json;charset=UTF-8");
                                return response.writeWith(Mono.just(getError(response)));
                            }
                        } else {
                            response.getHeaders().add("Content-Type", "application/json;charset=UTF-8");
                            return response.writeWith(Mono.just(getError(response)));
                        }
                    }
                    // 放行
                    return chain.filter(exchange);
                } else {
                    response.getHeaders().add("Content-Type", "application/json;charset=UTF-8");
                    return response.writeWith(Mono.just(getError(response)));
                }
            }
            // 1.获取请求参数
            //1.这里的request并不是servlet中的request
            //2.返回值是一个多键的map集合、也就是说这个map集合的键可以重复
//        MultiValueMap<String, String> params = exchange.getRequest().getQueryParams();
            // 2.获取userName参数
//        String userName = params.getFirst("userName");
//        // 3.校验
//        if ("root".equals(userName)) {
//            // 放行
//            return chain.filter(exchange);
//        }
            // 4.拦截
            // 4.1.禁止访问，设置状态码
//        exchange.getResponse().setStatusCode(HttpStatus.METHOD_NOT_ALLOWED);
//        // 4.2.结束处理
//        return exchange.getResponse().setComplete();
        }

    }

    @Override
    public int getOrder() {
        return 0;
    }

    @SneakyThrows
    private DataBuffer getError(ServerHttpResponse response) {
        //3.1设置状态码
        response.setStatusCode(HttpStatus.UNAUTHORIZED);

        //3.2封装返回数据
        Map<String, Object> map = new HashMap<>();
        map.put("message", "非法请求");
        map.put("status", 401);
        map.put("data", null);

        // 创建ObjectMapper实例
        ObjectMapper objectMapper = new ObjectMapper();
        //3.3作JSON转换
        byte[] bytes = objectMapper.writeValueAsString(map).getBytes(StandardCharsets.UTF_8);

//            byte[] bytes = JacksonUtils.toJsonBytes(map);
        //3.4调用bufferFactory方法,生成DataBuffer对象
        DataBuffer buffer = response.bufferFactory().wrap(bytes);
        return buffer;
    }
}
